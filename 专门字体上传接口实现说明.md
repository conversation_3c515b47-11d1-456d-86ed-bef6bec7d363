# 专门字体上传接口实现说明

## 🎯 实现概述

根据您的要求，我已经为低代码平台实现了专门的字体管理功能，包括：

1. **专门的字体上传接口** - 不再使用图片上传接口
2. **统一的字体文件存储** - 所有字体文件存储在 `uploads/font` 目录下
3. **完整的字体管理系统** - 支持系统字体、Web字体和自定义字体

## 📁 文件结构

### 后端文件
```
src/main/java/com/web/lowcode/
├── entity/Font.java                    # 字体实体类
├── mapper/FontMapper.java              # 字体Mapper接口
├── service/FontService.java            # 字体服务接口
├── service/impl/FontServiceImpl.java   # 字体服务实现
├── controller/FontController.java      # 字体控制器
└── config/FontResourceConfig.java      # 字体静态资源配置

src/main/resources/
├── mapper/FontMapper.xml               # MyBatis映射文件
├── db/font_tables_mysql8.sql          # 数据库表结构
└── application.properties              # 配置文件(已更新)
```

### 前端文件
```
low-code-vue/src/
├── api/font.js                         # 字体API(已更新)
├── components/editor/FontManager.vue   # 字体管理器(已更新)
└── store/index.js                      # 状态管理(已更新)
```

## 🔧 核心功能

### 1. 字体上传接口

**接口地址**: `POST /api/fonts/upload`

**参数**:
- `file`: 字体文件 (必需)
- `name`: 字体名称 (必需)
- `family`: 字体族 (必需)
- `description`: 字体描述 (可选)
- `previewText`: 预览文本 (可选)

**支持格式**: `.ttf`, `.otf`, `.woff`, `.woff2`
**文件大小限制**: 10MB

### 2. 字体存储路径

所有字体文件统一存储在：
```
F:\xiangmu\low-code\lowCode\uploads\font\
```

文件命名规则：
```
font_yyyyMMddHHmmss_随机8位字符.扩展名
例如: font_20240120143025_a1b2c3d4.ttf
```

### 3. 字体访问URL

字体文件可通过以下URL访问：
```
http://localhost:8080/api/fonts/file/{fileName}
```

## 📊 数据库设计

### 字体表 (font)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| name | VARCHAR(100) | 字体名称 |
| family | VARCHAR(200) | 字体族名称 |
| type | VARCHAR(20) | 字体类型(system/web/custom) |
| font_url | VARCHAR(500) | 字体文件URL |
| css_url | VARCHAR(500) | CSS文件URL(Web字体) |
| original_name | VARCHAR(255) | 原始文件名 |
| file_name | VARCHAR(255) | 存储文件名 |
| file_size | BIGINT | 文件大小 |
| file_format | VARCHAR(10) | 文件格式 |
| preview_text | VARCHAR(100) | 预览文本 |
| description | TEXT | 字体描述 |
| enabled | TINYINT | 是否启用 |
| sort_order | INT | 排序权重 |

## 🚀 使用方法

### 1. 初始化数据库

执行SQL文件创建字体管理相关表：
```sql
-- 执行文件: src/main/resources/db/font_tables_mysql8.sql
```

### 2. 启动后端服务

确保配置文件中的路径设置正确：
```properties
# application.properties
file.upload.path=uploads
font.upload.path=uploads/font
font.max-file-size=10485760
font.allowed-extensions=.ttf,.otf,.woff,.woff2
```

### 3. 前端使用

在字体管理器中：
1. 点击"添加字体"
2. 选择"自定义字体"
3. 上传字体文件
4. 填写字体信息
5. 点击确定

## 🔄 API接口列表

### 字体管理接口

| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/fonts/upload` | 上传自定义字体 |
| POST | `/api/fonts/web` | 添加Web字体 |
| POST | `/api/fonts/system` | 添加系统字体 |
| GET | `/api/fonts/page` | 分页查询字体 |
| GET | `/api/fonts/enabled` | 查询启用字体 |
| GET | `/api/fonts/type/{type}` | 按类型查询字体 |
| GET | `/api/fonts/{id}` | 查询字体详情 |
| PUT | `/api/fonts/{id}` | 更新字体信息 |
| DELETE | `/api/fonts/{id}` | 删除字体 |
| DELETE | `/api/fonts/batch` | 批量删除字体 |
| PUT | `/api/fonts/{id}/enabled` | 更新字体状态 |
| GET | `/api/fonts/file/{fileName}` | 访问字体文件 |

## 📝 前端API调用示例

### 上传字体文件
```javascript
import { fontApi } from '@/api/font'

// 上传字体文件
const uploadFont = async (file, name, family) => {
  try {
    const result = await fontApi.uploadFont(
      file,           // 字体文件
      name,           // 字体名称
      family,         // 字体族
      '自定义字体',    // 描述
      'Aa'            // 预览文本
    )
    console.log('字体上传成功:', result)
  } catch (error) {
    console.error('字体上传失败:', error)
  }
}
```

### 查询字体列表
```javascript
// 查询启用的字体
const loadFonts = async () => {
  try {
    const fonts = await fontApi.getEnabledFonts()
    console.log('字体列表:', fonts)
  } catch (error) {
    console.error('加载字体失败:', error)
  }
}
```

## 🔍 与原有图片接口的区别

### 原有方式 (图片接口)
```javascript
// ❌ 错误的方式 - 使用图片上传接口
const uploadResult = await imageApi.uploadImage(
  fontFile,
  IMAGE_CATEGORIES.FONT_FILE,
  'font',
  null,
  `字体文件: ${fontName}`
)
```

### 新的方式 (专门字体接口)
```javascript
// ✅ 正确的方式 - 使用专门的字体上传接口
const uploadResult = await fontApi.uploadFont(
  fontFile,
  fontName,
  fontFamily,
  description,
  previewText
)
```

## 🎨 功能特性

### 1. 字体类型支持
- **系统字体**: 预装的系统字体
- **Web字体**: 在线字体(Google Fonts等)
- **自定义字体**: 用户上传的字体文件

### 2. 文件管理
- 自动文件命名避免冲突
- 文件大小和格式验证
- 文件删除时自动清理

### 3. 字体加载
- 支持FontFace API加载
- CSS @font-face降级方案
- 字体加载状态管理

### 4. 数据统计
- 字体使用统计
- 字体类型统计
- 文件大小统计

## 🛡️ 安全特性

1. **文件类型验证**: 只允许字体文件格式
2. **文件大小限制**: 最大10MB
3. **路径安全**: 防止路径遍历攻击
4. **重名检查**: 防止字体名称和字体族重复

## 📈 性能优化

1. **文件缓存**: 字体文件缓存30天
2. **数据库索引**: 优化查询性能
3. **分页查询**: 支持大量字体数据
4. **延迟加载**: 按需加载字体文件

## 🔧 配置说明

### 存储路径配置
```properties
# 基础上传路径
file.upload.path=uploads

# 字体文件路径 (相对于基础路径)
font.upload.path=uploads/font

# 字体文件大小限制 (10MB)
font.max-file-size=10485760

# 支持的字体格式
font.allowed-extensions=.ttf,.otf,.woff,.woff2
```

### 服务器配置
```properties
# 文件上传大小限制
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
```

## ✅ 测试验证

### 1. 后端测试
- 启动后端服务
- 访问 Swagger UI: `http://localhost:8080/swagger-ui.html`
- 测试字体上传接口

### 2. 前端测试
- 启动前端服务
- 访问字体管理页面
- 测试字体文件上传功能

### 3. 文件验证
- 检查 `uploads/font` 目录
- 确认字体文件正确存储
- 验证字体文件可访问

## 🎯 总结

现在您的低代码平台拥有了完整的字体管理功能：

✅ **专门的字体上传接口** - 不再依赖图片接口  
✅ **统一的存储管理** - 所有字体文件在 `uploads/font` 目录  
✅ **完整的CRUD操作** - 增删改查字体信息  
✅ **多种字体类型支持** - 系统/Web/自定义字体  
✅ **安全的文件处理** - 格式验证和大小限制  
✅ **高性能的文件访问** - 缓存和静态资源配置  

这个实现完全符合您的需求，提供了专业的字体管理解决方案！🎉
