# 字体管理本地字体上传功能

## 功能概述

字体管理功能现已支持本地字体文件上传，用户可以上传自己的字体文件（.ttf、.otf、.woff、.woff2）到系统中，并在页面编辑器中使用这些字体。

## 功能特性

### 支持的字体类型
- **系统字体**: 预定义的系统字体（Arial、宋体、黑体等）
- **Web字体**: 通过URL加载的在线字体（Google Fonts等）
- **自定义字体**: 用户上传的本地字体文件

### 支持的文件格式
- `.ttf` - TrueType字体
- `.otf` - OpenType字体
- `.woff` - Web开放字体格式
- `.woff2` - Web开放字体格式2.0

### 文件大小限制
- 最大文件大小: 10MB

## 使用方法

### 1. 访问字体管理器

#### 方法一：通过样式编辑器
1. 在页面编辑器中选择一个文本组件
2. 在右侧属性面板中找到"样式"选项卡
3. 展开"字体"部分
4. 在字体族选择器旁边点击设置按钮（⚙️）

#### 方法二：直接访问测试页面
访问 `/font-test` 页面进行功能测试

### 2. 添加本地字体

1. 在字体管理器中点击"添加字体"按钮
2. 填写字体信息：
   - **字体名称**: 给字体起一个易识别的名称
   - **字体族**: 字体的CSS font-family值（会自动生成）
   - **字体类型**: 选择"自定义字体"
   - **字体文件**: 拖拽或点击上传字体文件
   - **预览文本**: 设置预览时显示的文本

3. 点击"确定"保存字体

### 3. 使用自定义字体

1. 在字体选择器中选择已上传的自定义字体
2. 系统会自动加载字体文件
3. 字体加载成功后即可在页面中使用

## 技术实现

### 前端实现

#### 字体文件上传
```javascript
// 处理字体文件变化
const handleFontFileChange = (file) => {
  fontForm.value.fontFile = file.raw
  
  // 自动设置字体名称和字体族
  if (!fontForm.value.name && file.name) {
    const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '')
    fontForm.value.name = nameWithoutExt
    fontForm.value.family = `"${nameWithoutExt}", sans-serif`
  }
}
```

#### 字体文件上传到服务器
```javascript
// 上传字体文件
const uploadResult = await imageApi.uploadImage(
  fontForm.value.fontFile,
  IMAGE_CATEGORIES.FONT_FILE, // 字体文件分类
  'font',
  null,
  `字体文件: ${fontForm.value.name}`
)
```

#### 字体加载和应用
```javascript
// 加载自定义字体
async loadCustomFont(font) {
  try {
    // 创建@font-face规则
    const fontFace = new FontFace(
      font.family.replace(/['"]/g, ''), 
      `url(${font.fontUrl})`
    )
    
    // 加载字体
    await fontFace.load()
    
    // 添加到文档字体集合
    document.fonts.add(fontFace)
    
    return true
  } catch (error) {
    // 降级方案：使用CSS @font-face
    const style = document.createElement('style')
    style.textContent = `
      @font-face {
        font-family: ${font.family.replace(/['"]/g, '')};
        src: url('${font.fontUrl}');
        font-display: swap;
      }
    `
    document.head.appendChild(style)
    return true
  }
}
```

### 后端实现

#### 文件存储
- 使用现有的图片上传接口存储字体文件
- 字体文件分类为 `IMAGE_CATEGORIES.FONT_FILE` (值为5)
- 支持MinIO和本地存储两种方式

#### 文件访问
- 通过文件访问接口提供字体文件的HTTP访问
- 支持跨域访问以便浏览器加载字体

## 组件结构

### FontManager.vue
字体管理器主组件，提供：
- 字体列表展示
- 添加/编辑/删除字体功能
- 字体文件上传功能
- 字体加载状态管理

### FontSelector.vue
字体选择器组件，提供：
- 字体分组显示（系统字体、Web字体、自定义字体）
- 字体预览功能
- 字体加载功能
- 字体管理器入口

### ComponentStylesEditor.vue
样式编辑器组件，集成了：
- FontSelector组件用于字体选择
- FontManager组件用于字体管理

## 数据流程

1. **上传字体文件**
   - 用户选择字体文件
   - 前端上传到后端存储服务
   - 获取文件访问URL

2. **保存字体信息**
   - 将字体信息保存到前端store
   - 包含字体名称、字体族、类型、文件URL等

3. **加载字体**
   - 用户选择字体时触发加载
   - 使用FontFace API或CSS @font-face加载字体
   - 标记字体为已加载状态

4. **应用字体**
   - 将字体族应用到组件样式
   - 实时预览字体效果

## 注意事项

1. **文件格式**: 确保上传的是有效的字体文件
2. **文件大小**: 注意文件大小限制，避免影响页面加载速度
3. **字体版权**: 确保使用的字体文件有合法的使用权限
4. **浏览器兼容性**: 不同浏览器对字体格式的支持可能不同
5. **加载性能**: 大量自定义字体可能影响页面加载性能

## 测试方法

1. 访问 `/font-test` 页面
2. 测试字体选择器功能
3. 测试字体管理器功能
4. 测试字体文件上传功能
5. 测试字体加载和应用功能

## 故障排除

### 字体上传失败
- 检查文件格式是否支持
- 检查文件大小是否超限
- 检查网络连接是否正常

### 字体加载失败
- 检查字体文件是否损坏
- 检查文件URL是否可访问
- 检查浏览器控制台错误信息

### 字体显示异常
- 检查字体族名称是否正确
- 检查CSS样式是否正确应用
- 尝试刷新页面重新加载字体
