import { defineStore } from 'pinia'
import { setToken, removeToken, setCurrentUser, removeCurrentUser } from '../utils/auth'
import router from '../router'
import { componentApi } from '../api/component'
import { componentCategoryApi } from '../api/componentCategory'

// User store
export const useUserStore = defineStore('user', {
  state: () => ({
    id: null,
    username: '',
    nickname: '',
    token: '',
    isLoggedIn: false
  }),

  actions: {
    setUserInfo(userInfo) {
      this.id = userInfo.id
      this.username = userInfo.username
      this.nickname = userInfo.nickname
      this.isLoggedIn = true

      // Also save to local storage
      setCurrentUser(userInfo)
    },

    setToken(token) {
      this.token = token

      // Also save to local storage
      setToken(token)
    },

    logout() {
      this.id = null
      this.username = ''
      this.nickname = ''
      this.token = ''
      this.isLoggedIn = false

      // Also clear from local storage
      removeToken()
      removeCurrentUser()

      // Redirect to login page
      router.push('/login')
    }
  }
})

// Editor store
export const useEditorStore = defineStore('editor', {
  state: () => ({
    // Current project and page
    projectId: null,
    pageId: null,

    // Canvas configuration
    canvasWidth: 375,
    canvasHeight: 667,
    canvasScale: 1,

    // Canvas scroll configuration
    canvasScrollConfig: {
      enableVerticalScroll: true,
      enableHorizontalScroll: false
    },

    // Font management
    fontLibrary: [
      // 系统默认字体
      { id: 'system-default', name: '系统默认', family: 'system-ui, -apple-system, sans-serif', type: 'system', preview: 'Aa' },
      { id: 'arial', name: 'Arial', family: 'Arial, sans-serif', type: 'system', preview: 'Aa' },
      { id: 'helvetica', name: 'Helvetica', family: 'Helvetica, Arial, sans-serif', type: 'system', preview: 'Aa' },
      { id: 'times', name: 'Times New Roman', family: 'Times, "Times New Roman", serif', type: 'system', preview: 'Aa' },
      { id: 'georgia', name: 'Georgia', family: 'Georgia, serif', type: 'system', preview: 'Aa' },
      { id: 'courier', name: 'Courier New', family: '"Courier New", Courier, monospace', type: 'system', preview: 'Aa' },
      { id: 'verdana', name: 'Verdana', family: 'Verdana, Geneva, sans-serif', type: 'system', preview: 'Aa' },

      // 中文字体
      { id: 'pingfang', name: '苹方', family: 'PingFang SC, "Hiragino Sans GB", "Microsoft YaHei", sans-serif', type: 'system', preview: '字体' },
      { id: 'heiti', name: '黑体', family: '"SimHei", "Microsoft YaHei", sans-serif', type: 'system', preview: '字体' },
      { id: 'songti', name: '宋体', family: '"SimSun", serif', type: 'system', preview: '字体' },
      { id: 'kaiti', name: '楷体', family: '"KaiTi", "STKaiti", serif', type: 'system', preview: '字体' },

      // Web字体（Google Fonts等）
      { id: 'roboto', name: 'Roboto', family: 'Roboto, sans-serif', type: 'web', url: 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap', preview: 'Aa' },
      { id: 'open-sans', name: 'Open Sans', family: '"Open Sans", sans-serif', type: 'web', url: 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap', preview: 'Aa' },
      { id: 'lato', name: 'Lato', family: 'Lato, sans-serif', type: 'web', url: 'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap', preview: 'Aa' }
    ],

    // 已加载的字体
    loadedFonts: new Set(),

    // Component library (loaded from database)
    componentLibrary: [],

    // Default component library (used for initialization)
    defaultComponentLibrary: [
      // 新增组件 - 基础组件
      {
        type: 'tag',
        name: '标记',
        icon: 'Price',
        defaultProps: {
          content: '标签内容',
          type: 'success',
          effect: 'light',
          closable: false
        },
        defaultStyles: {
          margin: '5px'
        }
      },
      {
        type: 'badge',
        name: '徽章',
        icon: 'Bell',
        defaultProps: {
          value: 1,
          max: 99,
          isDot: false,
          hidden: false
        },
        defaultStyles: {
          margin: '5px'
        }
      },
      {
        type: 'alert',
        name: '警告',
        icon: 'Warning',
        defaultProps: {
          title: '提示',
          description: '这是一条警告提示',
          type: 'warning',
          closable: true,
          showIcon: true
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      // 基础组件
      {
        type: 'text',
        name: '文本',
        icon: 'Document',
        defaultProps: {
          content: '文本内容'
        },
        defaultStyles: {
          fontSize: '16px',
          color: '#333',
          textAlign: 'left',
          margin: '0'
        }
      },
      {
        type: 'heading',
        name: '标题',
        icon: 'Reading',
        defaultProps: {
          content: '标题文本',
          level: 2
        },
        defaultStyles: {
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#303133',
          margin: '16px 0',
          textAlign: 'left'
        }
      },
      {
        type: 'paragraph',
        name: '段落',
        icon: 'Notebook',
        defaultProps: {
          content: '这是一个段落文本，可以包含多行内容。双击编辑文本内容。'
        },
        defaultStyles: {
          fontSize: '14px',
          lineHeight: '1.6',
          color: '#606266',
          margin: '12px 0',
          textAlign: 'left'
        }
      },
      {
        type: 'image',
        name: '图片',
        icon: 'Picture',
        defaultProps: {
          src: 'https://via.placeholder.com/200x100',
          alt: '图片'
        },
        defaultStyles: {
          width: '100%',
          height: 'auto',
          margin: '10px 0',
          borderRadius: '4px'
        }
      },
      {
        type: 'divider',
        name: '分割线',
        icon: 'SplitCells',
        defaultProps: {
          direction: 'horizontal',
          contentPosition: 'center',
          content: ''
        },
        defaultStyles: {
          margin: '20px 0',
          borderColor: '#dcdfe6'
        }
      },
      {
        type: 'button',
        name: '按钮',
        icon: 'Pointer',
        defaultProps: {
          text: '按钮',
          type: 'primary',
          size: 'default',
          plain: false,
          round: false,
          disabled: false,
          link: ''
        },
        defaultStyles: {
          margin: '10px 0',
          width: 'auto'
        }
      },
      {
        type: 'link',
        name: '链接',
        icon: 'Link',
        defaultProps: {
          text: '链接文本',
          href: 'https://example.com',
          target: '_blank',
          underline: true
        },
        defaultStyles: {
          color: '#409eff',
          fontSize: '14px',
          margin: '5px 0'
        }
      },
      {
        type: 'icon',
        name: '图标',
        icon: 'Star',
        defaultProps: {
          name: 'Star',
          size: 'default',
          color: ''
        },
        defaultStyles: {
          margin: '5px',
          fontSize: '20px',
          color: '#409eff'
        }
      },

      // 新增组件 - 表单组件
      {
        type: 'date-picker',
        name: '日期选择器',
        icon: 'Calendar',
        defaultProps: {
          type: 'date',
          placeholder: '选择日期',
          format: 'YYYY-MM-DD',
          clearable: true,
          disabled: false
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'time-picker',
        name: '时间选择器',
        icon: 'Timer',
        defaultProps: {
          placeholder: '选择时间',
          clearable: true,
          disabled: false
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'color-picker',
        name: '颜色选择器',
        icon: 'Brush',
        defaultProps: {
          showAlpha: true,
          colorFormat: 'hex'
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      {
        type: 'upload',
        name: '上传',
        icon: 'Upload',
        defaultProps: {
          action: '',
          multiple: false,
          accept: '',
          listType: 'text',
          autoUpload: true,
          limit: 5
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      // 表单组件
      {
        type: 'input',
        name: '输入框',
        icon: 'EditPen',
        defaultProps: {
          placeholder: '请输入内容',
          type: 'text',
          clearable: true,
          disabled: false,
          maxlength: 100,
          showWordLimit: false
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'textarea',
        name: '文本域',
        icon: 'Memo',
        defaultProps: {
          placeholder: '请输入内容',
          rows: 4,
          clearable: true,
          disabled: false,
          maxlength: 500,
          showWordLimit: true
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'select',
        name: '选择器',
        icon: 'Select',
        defaultProps: {
          placeholder: '请选择',
          options: [
            { label: '选项1', value: '1' },
            { label: '选项2', value: '2' },
            { label: '选项3', value: '3' }
          ],
          clearable: true,
          disabled: false
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'radio',
        name: '单选框组',
        icon: 'CircleCheck',
        defaultProps: {
          options: [
            { label: '选项1', value: '1' },
            { label: '选项2', value: '2' },
            { label: '选项3', value: '3' }
          ],
          disabled: false
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      {
        type: 'checkbox',
        name: '复选框组',
        icon: 'Check',
        defaultProps: {
          options: [
            { label: '选项1', value: '1' },
            { label: '选项2', value: '2' },
            { label: '选项3', value: '3' }
          ],
          disabled: false
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      {
        type: 'switch',
        name: '开关',
        icon: 'Switch',
        defaultProps: {
          activeText: '开启',
          inactiveText: '关闭',
          disabled: false
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      {
        type: 'slider',
        name: '滑块',
        icon: 'Operation',
        defaultProps: {
          min: 0,
          max: 100,
          step: 1,
          showStops: false,
          showInput: true,
          disabled: false
        },
        defaultStyles: {
          width: '100%',
          margin: '20px 0'
        }
      },
      {
        type: 'rate',
        name: '评分',
        icon: 'StarFilled',
        defaultProps: {
          max: 5,
          disabled: false,
          allowHalf: false,
          showText: false
        },
        defaultStyles: {
          margin: '10px 0',
          color: '#ff9900'
        }
      },

      // 新增组件 - 布局组件
      {
        type: 'divider',
        name: '分割线',
        icon: 'DCaret',
        defaultProps: {
          direction: 'horizontal',
          contentPosition: 'center',
          content: ''
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'space',
        name: '间距',
        icon: 'Aim',
        defaultProps: {
          direction: 'horizontal',
          size: 'medium',
          wrap: false,
          alignment: 'center'
        },
        defaultStyles: {
          margin: '10px 0'
        }
      },
      {
        type: 'drawer',
        name: '抽屉',
        icon: 'Right',
        defaultProps: {
          title: '抽屉标题',
          direction: 'rtl',
          size: '30%',
          withHeader: true,
          destroyOnClose: false,
          modal: true,
          showClose: true,
          closeOnClickModal: true,
          closeOnPressEscape: true
        },
        defaultStyles: {}
      },
      // 布局组件
      {
        type: 'container',
        name: '容器',
        icon: 'Grid',
        defaultProps: {
          children: []
        },
        defaultStyles: {
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          minHeight: '100px',
          padding: '10px',
          border: '1px dashed #ccc',
          boxSizing: 'border-box',
          backgroundColor: 'rgba(255, 255, 255, 0.8)'
        }
      },
      {
        type: 'row',
        name: '行',
        icon: 'Menu',
        defaultProps: {
          gutter: 20,
          justify: 'start',
          align: 'top',
          children: []
        },
        defaultStyles: {
          display: 'flex',
          width: '100%',
          minHeight: '50px',
          margin: '10px 0'
        }
      },
      {
        type: 'column',
        name: '列',
        icon: 'Crop',
        defaultProps: {
          span: 12,
          offset: 0,
          children: []
        },
        defaultStyles: {
          padding: '10px',
          border: '1px dashed #dcdfe6',
          minHeight: '50px'
        }
      },
      {
        type: 'card',
        name: '卡片',
        icon: 'CreditCard',
        defaultProps: {
          title: '卡片标题',
          shadow: 'always',
          children: []
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0',
          borderRadius: '4px',
          overflow: 'hidden'
        }
      },
      {
        type: 'tabs',
        name: '标签页',
        icon: 'Files',
        defaultProps: {
          type: 'border-card',
          tabs: [
            { label: '标签1', name: 'tab1', content: '标签1内容' },
            { label: '标签2', name: 'tab2', content: '标签2内容' }
          ]
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },

      // 新增组件 - 高级组件
      {
        type: 'steps',
        name: '步骤条',
        icon: 'Guide',
        defaultProps: {
          active: 0,
          direction: 'horizontal',
          simple: false,
          items: [
            { title: '步骤1', description: '步骤1描述' },
            { title: '步骤2', description: '步骤2描述' },
            { title: '步骤3', description: '步骤3描述' }
          ]
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'result',
        name: '结果',
        icon: 'CircleCheck',
        defaultProps: {
          title: '操作成功',
          subTitle: '请根据提示进行操作',
          icon: 'success',
          status: 'success'
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'empty',
        name: '空状态',
        icon: 'Delete',
        defaultProps: {
          description: '暂无数据',
          image: ''
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'descriptions',
        name: '描述列表',
        icon: 'Document',
        defaultProps: {
          title: '用户信息',
          column: 3,
          border: true,
          direction: 'horizontal',
          size: 'default',
          items: [
            { label: '用户名', value: '张三' },
            { label: '手机号', value: '13800138000' },
            { label: '地址', value: '北京市朝阳区' }
          ]
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      // 高级组件
      {
        type: 'table',
        name: '表格',
        icon: 'Grid',
        defaultProps: {
          columns: [
            { label: '日期', prop: 'date' },
            { label: '姓名', prop: 'name' },
            { label: '地址', prop: 'address' }
          ],
          data: [
            { date: '2023-01-01', name: '张三', address: '北京市朝阳区' },
            { date: '2023-01-02', name: '李四', address: '上海市浦东新区' }
          ],
          stripe: true,
          border: true
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'pagination',
        name: '分页',
        icon: 'Sort',
        defaultProps: {
          total: 100,
          pageSize: 10,
          currentPage: 1,
          layout: 'total, prev, pager, next'
        },
        defaultStyles: {
          margin: '20px 0',
          textAlign: 'center'
        }
      },
      {
        type: 'progress',
        name: '进度条',
        icon: 'TrendCharts',
        defaultProps: {
          percentage: 50,
          type: 'line',
          strokeWidth: 6,
          textInside: false,
          status: ''
        },
        defaultStyles: {
          margin: '15px 0'
        }
      },
      {
        type: 'timeline',
        name: '时间线',
        icon: 'Timer',
        defaultProps: {
          items: [
            { timestamp: '2023-01-01', content: '事件1', type: 'primary' },
            { timestamp: '2023-01-02', content: '事件2', type: 'success' },
            { timestamp: '2023-01-03', content: '事件3', type: 'warning' }
          ]
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'carousel',
        name: '轮播图',
        icon: 'PictureFilled',
        defaultProps: {
          height: 200,
          autoplay: true,
          interval: 3000,
          items: [
            { url: 'https://via.placeholder.com/800x200/409eff/ffffff?text=Slide+1' },
            { url: 'https://via.placeholder.com/800x200/67c23a/ffffff?text=Slide+2' },
            { url: 'https://via.placeholder.com/800x200/e6a23c/ffffff?text=Slide+3' }
          ]
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },

      // 新增组件 - 数据组件
      {
        type: 'statistic',
        name: '统计数值',
        icon: 'DataLine',
        defaultProps: {
          title: '访问量',
          value: 112893,
          precision: 0,
          prefix: '',
          suffix: '',
          valueStyle: {
            color: '#3f8600'
          }
        },
        defaultStyles: {
          margin: '20px 0'
        }
      },
      {
        type: 'line-chart',
        name: '折线图',
        icon: 'TrendCharts',
        defaultProps: {
          title: '销售趋势',
          xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          series: [
            { name: '本周', data: [120, 132, 101, 134, 90, 230, 210] },
            { name: '上周', data: [220, 182, 191, 234, 290, 330, 310] }
          ]
        },
        defaultStyles: {
          height: '300px',
          margin: '20px 0'
        }
      },
      {
        type: 'bar-chart',
        name: '柱状图',
        icon: 'Histogram',
        defaultProps: {
          title: '销售额',
          xAxis: ['一月', '二月', '三月', '四月', '五月', '六月'],
          series: [
            { name: '2022年', data: [320, 302, 301, 334, 390, 330] },
            { name: '2023年', data: [120, 132, 101, 134, 90, 230] }
          ]
        },
        defaultStyles: {
          height: '300px',
          margin: '20px 0'
        }
      },
      {
        type: 'pie-chart',
        name: '饼图',
        icon: 'PieChart',
        defaultProps: {
          title: '访问来源',
          series: [
            {
              name: '访问来源',
              data: [
                { value: 1048, name: '搜索引擎' },
                { value: 735, name: '直接访问' },
                { value: 580, name: '邮件营销' },
                { value: 484, name: '联盟广告' },
                { value: 300, name: '视频广告' }
              ]
            }
          ]
        },
        defaultStyles: {
          height: '300px',
          margin: '20px 0'
        }
      },
      // 流程组件
      {
        type: 'flowNode',
        name: '流程节点',
        icon: 'Connection',
        defaultProps: {
          title: '节点标题',
          description: '节点描述'
        },
        defaultStyles: {
          backgroundColor: '#ffffff',
          borderColor: '#409eff',
          borderWidth: '2px',
          borderStyle: 'solid',
          borderRadius: '4px',
          padding: '10px',
          width: '150px',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'flowConnection',
        name: '流程连线',
        icon: 'Right',
        defaultProps: {
          label: '',
          orientation: 'horizontal',
          type: 'straight',
          style: 'solid',
          source: null,
          target: null
        },
        defaultStyles: {
          width: '100px',
          height: '2px',
          backgroundColor: '#409eff',
          margin: '15px',
          borderColor: '#409eff'
        }
      },
      {
        type: 'flowStart',
        name: '开始节点',
        icon: 'VideoPlay',
        defaultProps: {
          title: '开始'
        },
        defaultStyles: {
          backgroundColor: '#67c23a',
          color: '#ffffff',
          borderRadius: '50%',
          width: '60px',
          height: '60px',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'flowEnd',
        name: '结束节点',
        icon: 'CircleClose',
        defaultProps: {
          title: '结束'
        },
        defaultStyles: {
          backgroundColor: '#f56c6c',
          color: '#ffffff',
          borderRadius: '50%',
          width: '60px',
          height: '60px',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'flowDecision',
        name: '决策节点',
        icon: 'SwitchButton',
        defaultProps: {
          title: '决策',
          description: '条件判断',
          branches: [
            { label: '是' },
            { label: '否' }
          ]
        },
        defaultStyles: {
          backgroundColor: '#ffffff',
          borderColor: '#e6a23c',
          borderWidth: '2px',
          borderStyle: 'solid',
          width: '120px',
          height: '120px',
          transform: 'rotate(45deg)',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'flowProcess',
        name: '流程节点',
        icon: 'Operation',
        defaultProps: {
          title: '流程',
          description: '流程描述',
          icon: 'Operation',
          processingTime: 0,
          assignee: ''
        },
        defaultStyles: {
          backgroundColor: '#ffffff',
          borderColor: '#409eff',
          borderWidth: '2px',
          borderStyle: 'solid',
          borderRadius: '4px',
          padding: '15px',
          width: '180px',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'collapse',
        name: '折叠面板',
        icon: 'Fold',
        defaultProps: {
          accordion: false,
          items: [
            { title: '面板标题1', content: '面板内容1' },
            { title: '面板标题2', content: '面板内容2' },
            { title: '面板标题3', content: '面板内容3' }
          ]
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      }
    ],

    // Current page configuration
    pageConfig: {
      name: '',
      title: '',
      path: '',
      components: []
    },

    // Selected component
    selectedComponent: null
  }),

  actions: {
    setProject(projectId) {
      this.projectId = projectId
    },

    setPage(pageId) {
      this.pageId = pageId
    },

    setPageConfig(config) {
      this.pageConfig = config
    },

    addComponent(component) {
      this.pageConfig.components.push(component)
    },

    updateComponent(index, component) {
      this.pageConfig.components[index] = component
    },

    removeComponent(index) {
      this.pageConfig.components.splice(index, 1)
    },

    selectComponent(component) {
      this.selectedComponent = component
    },

    clearSelection() {
      this.selectedComponent = null
    },

    setCanvasScale(scale) {
      this.canvasScale = scale
    },

    setCanvasScrollConfig(config) {
      this.canvasScrollConfig = { ...this.canvasScrollConfig, ...config }
    },

    // Font management actions
    addFont(font) {
      const newFont = {
        id: font.id || `custom-${Date.now()}`,
        name: font.name,
        family: font.family,
        type: font.type || 'custom',
        url: font.url,
        preview: font.preview || 'Aa'
      }
      this.fontLibrary.push(newFont)
    },

    removeFont(fontId) {
      const index = this.fontLibrary.findIndex(font => font.id === fontId)
      if (index > -1) {
        this.fontLibrary.splice(index, 1)
      }
    },

    updateFont(fontId, updates) {
      const index = this.fontLibrary.findIndex(font => font.id === fontId)
      if (index > -1) {
        this.fontLibrary[index] = { ...this.fontLibrary[index], ...updates }
      }
    },

    async loadWebFont(font) {
      if (this.loadedFonts.has(font.id)) {
        return true
      }

      try {
        if (font.type === 'web' && font.url) {
          // 加载Web字体（CSS链接）
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = font.url
          document.head.appendChild(link)

          // 等待字体加载完成
          await new Promise((resolve, reject) => {
            link.onload = resolve
            link.onerror = reject
            setTimeout(reject, 5000) // 5秒超时
          })
        } else if (font.type === 'custom' && font.fontUrl) {
          // 加载自定义字体文件
          await this.loadCustomFont(font)
        }

        this.loadedFonts.add(font.id)
        return true
      } catch (error) {
        console.error('Failed to load font:', font.name, error)
        return false
      }
    },

    async loadCustomFont(font) {
      try {
        // 创建@font-face规则
        const fontFace = new FontFace(font.family.replace(/['"]/g, ''), `url(${font.fontUrl})`)

        // 加载字体
        await fontFace.load()

        // 添加到文档字体集合
        document.fonts.add(fontFace)

        console.log('Custom font loaded successfully:', font.name)
        return true
      } catch (error) {
        console.error('Failed to load custom font:', font.name, error)

        // 降级方案：使用CSS @font-face
        try {
          const style = document.createElement('style')
          style.textContent = `
            @font-face {
              font-family: ${font.family.replace(/['"]/g, '')};
              src: url('${font.fontUrl}');
              font-display: swap;
            }
          `
          document.head.appendChild(style)
          return true
        } catch (fallbackError) {
          console.error('Fallback font loading also failed:', fallbackError)
          throw fallbackError
        }
      }
    },

    // Load components from database via API
    async loadComponentsFromDb(forceRefresh = false) {
      try {
        console.log('Attempting to load components from API...')
        // Get all components from the backend API
        const apiComponents = await componentApi.getAllComponents()
        console.log('API response for components:', apiComponents)

        if (apiComponents && apiComponents.length > 0) {
          // If there are components from the API, use them
          this.componentLibrary = apiComponents
          console.log('Loaded components from API:', apiComponents.length)

          // Log the loaded components for debugging
          console.log('Component types loaded:', apiComponents.map(c => c.type).join(', '))

          // Log enabled/disabled components
          const enabledCount = apiComponents.filter(c => c.enabled === 1 || c.enabled === '1').length
          const disabledCount = apiComponents.length - enabledCount
          console.log(`Loaded ${apiComponents.length} components: ${enabledCount} enabled, ${disabledCount} disabled`)

          // Also load component categories
          await this.loadComponentCategories()

          return true
        } else {
          console.log('No components returned from API, using default components')
          // If there are no components from the API, initialize with default components
          await this.initializeDefaultComponents()
          return false
        }
      } catch (error) {
        console.error('Failed to load components from API:', error)
        console.log('Using default components due to API error')
        await this.initializeDefaultComponents()
        return false
      }
    },

    // Get default components
    getDefaultComponents() {
      // Return a deep copy of the default components
      return JSON.parse(JSON.stringify(this.defaultComponentLibrary || []))
    },

    // Load component categories from database via API
    async loadComponentCategories() {
      try {
        console.log('Attempting to load component categories from API...')
        // Get all component categories from the backend API
        const categories = await componentCategoryApi.getAllCategories()
        console.log('API response for component categories:', categories)

        if (categories && categories.length > 0) {
          // If there are categories from the API, store them
          console.log('Loaded component categories from API:', categories.length)

          // Log enabled/disabled categories
          const enabledCount = categories.filter(c => c.enabled === 1 || c.enabled === '1').length
          const disabledCount = categories.length - enabledCount
          console.log(`Loaded ${categories.length} categories: ${enabledCount} enabled, ${disabledCount} disabled`)

          // You can store categories in the store if needed
          return true
        } else {
          console.log('No component categories returned from API')
          return false
        }
      } catch (error) {
        console.error('Failed to load component categories from API:', error)
        return false
      }
    },

    // Initialize default components in database via API
    async initializeDefaultComponents() {
      try {
        console.log('Initializing default components...')
        // Get the default components
        const defaultComponents = this.getDefaultComponents()
        console.log('Default components to initialize:', defaultComponents)

        // For now, skip API calls and just use the default components directly
        console.log('Skipping API calls for now, using default components directly')
        this.componentLibrary = defaultComponents
        console.log('Component library initialized with default components')

        return true

        /* Commented out API calls for now
        // Create each default component via API
        const createPromises = defaultComponents.map(async (component) => {
          try {
            // Check if component already exists
            const existingComponent = await componentApi.getComponentByType(component.type)
            if (existingComponent) {
              console.log(`Component ${component.type} already exists, skipping`)
              return existingComponent
            }

            // Create the component
            return await componentApi.createComponent(component)
          } catch (error) {
            console.error(`Failed to create component ${component.type}:`, error)
            return null
          }
        })

        // Wait for all components to be created
        const createdComponents = await Promise.all(createPromises)

        // Filter out null values (failed creations)
        const validComponents = createdComponents.filter(c => c !== null)

        // Update the component library with the created components
        if (validComponents.length > 0) {
          this.componentLibrary = validComponents
        } else {
          // If no components were created, use the default components
          this.componentLibrary = defaultComponents
        }
        */
      } catch (error) {
        console.error('Failed to initialize default components:', error)
        console.log('Using default components due to initialization error')

        // In case of error, keep using the default components
        const defaultComponents = this.getDefaultComponents()
        console.log('Default components:', defaultComponents)
        this.componentLibrary = defaultComponents
        return false
      }
    },

    // Add or update a component in the library via API
    async saveComponentToLibrary(component) {
      try {
        let savedComponent;

        // Check if component already exists
        const existingComponent = await componentApi.getComponentByType(component.type)

        if (existingComponent) {
          // Update existing component
          savedComponent = await componentApi.updateComponent({
            ...component,
            id: existingComponent.id
          })
        } else {
          // Create new component
          savedComponent = await componentApi.createComponent(component)
        }

        // Update the component in the local library
        const index = this.componentLibrary.findIndex(c => c.type === component.type)
        if (index !== -1) {
          // Update existing component
          this.componentLibrary[index] = savedComponent
        } else {
          // Add new component
          this.componentLibrary.push(savedComponent)
        }

        console.log('Saved component to library:', savedComponent.type)
        return savedComponent
      } catch (error) {
        console.error('Failed to save component to library:', error)
        throw error
      }
    },

    // Delete a component from the library via API
    async deleteComponentFromLibrary(type) {
      try {
        // Find the component in the local library
        const component = this.componentLibrary.find(c => c.type === type)
        if (!component) {
          throw new Error(`Component with type '${type}' not found`)
        }

        // Delete the component from the database
        await componentApi.deleteComponent(component.id)

        // Remove the component from the local library
        const index = this.componentLibrary.findIndex(c => c.type === type)
        if (index !== -1) {
          this.componentLibrary.splice(index, 1)
        }

        console.log('Deleted component from library:', type)
        return true
      } catch (error) {
        console.error('Failed to delete component from library:', error)
        throw error
      }
    }
  }
})
