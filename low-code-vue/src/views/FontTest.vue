<template>
  <div class="font-test-page">
    <div class="header">
      <h1>字体管理功能测试</h1>
      <p>测试本地字体文件上传和管理功能</p>
    </div>

    <div class="test-sections">
      <!-- 字体选择器测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>字体选择器测试</span>
          </div>
        </template>
        
        <div class="test-content">
          <el-form label-width="100px">
            <el-form-item label="选择字体:">
              <font-selector
                v-model="selectedFont"
                @openFontManager="showFontManager"
              />
            </el-form-item>
            
            <el-form-item label="当前字体:">
              <el-input v-model="selectedFont" readonly />
            </el-form-item>
            
            <el-form-item label="预览文本:">
              <div 
                class="font-preview"
                :style="{ fontFamily: selectedFont }"
              >
                {{ previewText }}
              </div>
            </el-form-item>
            
            <el-form-item label="预览文本:">
              <el-input 
                v-model="previewText" 
                placeholder="输入要预览的文本"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 字体管理器测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>字体管理器测试</span>
          </div>
        </template>
        
        <div class="test-content">
          <el-button 
            type="primary" 
            @click="showFontManager"
            icon="Setting"
          >
            打开字体管理器
          </el-button>
          
          <el-divider />
          
          <div class="font-list">
            <h4>当前字体库:</h4>
            <el-table :data="fontLibrary" style="width: 100%" max-height="300">
              <el-table-column prop="name" label="字体名称" width="150" />
              <el-table-column prop="family" label="字体族" width="200" show-overflow-tooltip />
              <el-table-column prop="type" label="类型" width="80">
                <template #default="{ row }">
                  <el-tag 
                    :type="getTypeTagType(row.type)" 
                    size="small"
                  >
                    {{ getTypeLabel(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="预览" width="100">
                <template #default="{ row }">
                  <span
                    class="font-preview-text"
                    :style="{ fontFamily: row.family }"
                  >
                    {{ row.preview }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button
                    v-if="row.type === 'custom'"
                    size="small"
                    type="info"
                    @click="diagnoseFont(row)"
                  >
                    诊断
                  </el-button>
                  <el-button
                    v-if="row.type === 'custom'"
                    size="small"
                    @click="testSingleFont(row)"
                  >
                    测试
                  </el-button>
                  <el-button
                    v-if="row.type === 'custom'"
                    size="small"
                    type="warning"
                    @click="debugSingleFont(row)"
                  >
                    调试
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 字体加载状态测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>字体加载状态测试</span>
          </div>
        </template>
        
        <div class="test-content">
          <div class="loaded-fonts">
            <h4>已加载的字体:</h4>
            <div v-if="loadedFonts.size === 0" class="no-fonts">
              暂无已加载的字体
            </div>
            <div v-else class="font-tags">
              <el-tag 
                v-for="fontId in Array.from(loadedFonts)" 
                :key="fontId"
                type="success"
                class="font-tag"
              >
                {{ getFontNameById(fontId) }}
              </el-tag>
            </div>
          </div>
          
          <el-divider />
          
          <div class="test-buttons">
            <el-button @click="testLoadAllFonts">测试加载所有字体</el-button>
            <el-button @click="clearLoadedFonts">清除已加载字体</el-button>
            <el-button type="warning" @click="debugFileSystem">调试文件系统</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 字体管理器组件 -->
    <font-manager v-model="fontManagerVisible" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useEditorStore } from '../store'
import { ElMessage, ElMessageBox } from 'element-plus'
import FontSelector from '../components/editor/FontSelector.vue'
import FontManager from '../components/editor/FontManager.vue'
import { diagnoseFontIssues } from '../utils/fontDiagnostic'
import { debugFont } from '../utils/fontDebugger'
import { fontApi } from '../api/font'

const editorStore = useEditorStore()

// 响应式数据
const selectedFont = ref('')
const previewText = ref('这是字体预览文本 - Font Preview Text - 123')
const fontManagerVisible = ref(false)

// 计算属性
const fontLibrary = computed(() => editorStore.fontLibrary)
const loadedFonts = computed(() => editorStore.loadedFonts)

// 方法
const showFontManager = () => {
  fontManagerVisible.value = true
}

const getTypeTagType = (type) => {
  switch (type) {
    case 'system': return 'success'
    case 'web': return 'primary'
    case 'custom': return 'warning'
    default: return 'info'
  }
}

const getTypeLabel = (type) => {
  switch (type) {
    case 'system': return '系统'
    case 'web': return 'Web'
    case 'custom': return '自定义'
    default: return '未知'
  }
}

const getFontNameById = (fontId) => {
  const font = fontLibrary.value.find(f => f.id === fontId)
  return font ? font.name : fontId
}

const testLoadAllFonts = async () => {
  ElMessage.info('开始测试加载所有字体...')
  
  let successCount = 0
  let failCount = 0
  
  for (const font of fontLibrary.value) {
    if (font.type === 'web' || font.type === 'custom') {
      try {
        const loaded = await editorStore.loadWebFont(font)
        if (loaded) {
          successCount++
        } else {
          failCount++
        }
      } catch (error) {
        console.error('Failed to load font:', font.name, error)
        failCount++
      }
    }
  }
  
  ElMessage.success(`字体加载测试完成: 成功 ${successCount} 个，失败 ${failCount} 个`)
}

const clearLoadedFonts = () => {
  editorStore.loadedFonts.clear()
  ElMessage.success('已清除所有已加载字体状态')
}

// 诊断单个字体
const diagnoseFont = async (font) => {
  try {
    ElMessage.info(`正在诊断字体: ${font.name}`)

    // 运行字体诊断
    const results = await diagnoseFontIssues(font)

    if (results.summary.success) {
      ElMessage.success(`字体 ${font.name} 诊断通过，没有发现问题`)
    } else {
      const issues = results.summary.issues.map(issue => issue.message).join('\n')
      ElMessageBox.alert(
        `发现 ${results.summary.failedTests} 个问题：\n\n${issues}\n\n请查看浏览器控制台获取详细信息`,
        `字体诊断结果: ${font.name}`,
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
    }
  } catch (error) {
    console.error('Font diagnosis failed:', error)
    ElMessage.error(`字体诊断失败: ${error.message}`)
  }
}

// 测试单个字体
const testSingleFont = async (font) => {
  try {
    ElMessage.info(`正在测试字体: ${font.name}`)

    const loaded = await editorStore.loadWebFont(font)
    if (loaded) {
      ElMessage.success(`字体 ${font.name} 测试通过`)

      // 更新预览文本使用这个字体
      selectedFont.value = font.family
    } else {
      ElMessage.error(`字体 ${font.name} 测试失败`)
    }
  } catch (error) {
    console.error('Font test failed:', error)
    ElMessage.error(`字体测试失败: ${error.message}`)
  }
}

// 调试单个字体
const debugSingleFont = async (font) => {
  try {
    ElMessage.info(`正在调试字体: ${font.name}`)

    // 运行快速调试
    const { results, suggestions } = await debugFont(font)

    // 统计问题
    const totalChecks = results.checks.length
    const failedChecks = results.checks.filter(check => !check.passed).length

    if (failedChecks === 0) {
      ElMessage.success(`字体 ${font.name} 调试通过，没有发现问题`)
    } else {
      // 收集所有问题
      const allIssues = results.checks
        .filter(check => !check.passed)
        .map(check => `${check.name}: ${check.issues.join(', ')}`)
        .join('\n')

      const suggestionText = suggestions.length > 0 ?
        `\n\n修复建议:\n${suggestions.join('\n')}` : ''

      ElMessageBox.alert(
        `发现 ${failedChecks}/${totalChecks} 个问题:\n\n${allIssues}${suggestionText}\n\n详细信息请查看浏览器控制台`,
        `字体调试结果: ${font.name}`,
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
    }
  } catch (error) {
    console.error('Font debug failed:', error)
    ElMessage.error(`字体调试失败: ${error.message}`)
  }
}

// 调试文件系统
const debugFileSystem = async () => {
  try {
    ElMessage.info('正在调试文件系统...')

    const result = await fontApi.debugFileSystem()

    if (result && result.data) {
      const data = result.data

      let message = `文件系统调试结果:\n\n`
      message += `用户目录: ${data.userDir}\n`
      message += `字体目录: ${data.fontDirPath}\n`
      message += `目录存在: ${data.fontDirExists ? '是' : '否'}\n`
      message += `目录可读: ${data.fontDirCanRead ? '是' : '否'}\n`
      message += `目录可写: ${data.fontDirCanWrite ? '是' : '否'}\n\n`

      if (data.files && Array.isArray(data.files)) {
        message += `文件列表 (${data.files.length} 个文件):\n`
        data.files.forEach(file => {
          message += `- ${file.name} (${file.size} bytes, 可读: ${file.canRead ? '是' : '否'})\n`
        })
      } else {
        message += `文件列表: ${data.files || '无法读取'}\n`
      }

      ElMessageBox.alert(
        message,
        '文件系统调试结果',
        {
          confirmButtonText: '确定',
          type: data.fontDirExists ? 'success' : 'error'
        }
      )
    } else {
      ElMessage.error('文件系统调试失败：无返回数据')
    }
  } catch (error) {
    console.error('File system debug failed:', error)
    ElMessage.error(`文件系统调试失败: ${error.message || '未知错误'}`)
  }
}
</script>

<style scoped>
.font-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.header p {
  color: #606266;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-card {
  width: 100%;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.test-content {
  padding: 10px 0;
}

.font-preview {
  font-size: 24px;
  font-weight: 500;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.font-preview-text {
  font-size: 16px;
  font-weight: 500;
}

.font-list h4 {
  margin-bottom: 15px;
  color: #303133;
}

.loaded-fonts h4 {
  margin-bottom: 15px;
  color: #303133;
}

.no-fonts {
  color: #909399;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.font-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.font-tag {
  margin: 0;
}

.test-buttons {
  display: flex;
  gap: 10px;
}
</style>
