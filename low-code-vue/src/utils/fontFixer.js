/**
 * 字体问题修复工具
 * 提供自动修复常见字体问题的功能
 */

export class FontFixer {
  constructor() {
    this.fixHistory = []
  }

  /**
   * 自动修复字体问题
   * @param {Object} font - 字体对象
   * @returns {Object} 修复结果
   */
  async autoFix(font) {
    console.log('🔧 开始自动修复字体问题:', font.name)
    
    const fixes = []
    let success = false

    try {
      // 1. 修复字体族名称
      const familyFix = this.fixFontFamily(font)
      if (familyFix.applied) {
        fixes.push(familyFix)
      }

      // 2. 修复字体URL
      const urlFix = await this.fixFontUrl(font)
      if (urlFix.applied) {
        fixes.push(urlFix)
      }

      // 3. 强制重新加载字体
      const reloadFix = await this.forceReloadFont(font)
      if (reloadFix.applied) {
        fixes.push(reloadFix)
      }

      // 4. 清除字体缓存
      const cacheFix = this.clearFontCache(font)
      if (cacheFix.applied) {
        fixes.push(cacheFix)
      }

      // 5. 验证修复结果
      const verifyResult = await this.verifyFontFix(font)
      success = verifyResult.success

      const result = {
        font: font,
        success: success,
        fixes: fixes,
        verification: verifyResult,
        timestamp: new Date().toISOString()
      }

      this.fixHistory.push(result)
      
      console.log('🔧 字体修复完成:', result)
      return result

    } catch (error) {
      console.error('🔧 字体修复失败:', error)
      return {
        font: font,
        success: false,
        error: error.message,
        fixes: fixes,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 修复字体族名称
   */
  fixFontFamily(font) {
    const originalFamily = font.family
    let fixedFamily = originalFamily
    let applied = false

    // 清理字体族名称
    if (fixedFamily) {
      // 移除多余的引号
      const cleaned = fixedFamily.replace(/['"]/g, '').trim()
      
      // 确保有后备字体
      if (!cleaned.includes(',')) {
        fixedFamily = `"${cleaned}", sans-serif`
        applied = true
      } else if (!fixedFamily.includes('"') && !fixedFamily.includes("'")) {
        // 为主字体添加引号
        const parts = fixedFamily.split(',')
        parts[0] = `"${parts[0].trim()}"`
        fixedFamily = parts.join(', ')
        applied = true
      }
    }

    if (applied) {
      font.family = fixedFamily
    }

    return {
      type: 'fontFamily',
      applied: applied,
      original: originalFamily,
      fixed: fixedFamily,
      description: applied ? '修复了字体族名称格式' : '字体族名称无需修复'
    }
  }

  /**
   * 修复字体URL
   */
  async fixFontUrl(font) {
    if (!font.fontUrl) {
      return {
        type: 'fontUrl',
        applied: false,
        description: '字体URL为空，无法修复'
      }
    }

    const originalUrl = font.fontUrl
    let fixedUrl = originalUrl
    let applied = false

    try {
      // 检查原始URL是否可访问
      const response = await fetch(originalUrl, { method: 'HEAD' })
      
      if (!response.ok) {
        // 尝试添加时间戳避免缓存
        fixedUrl = `${originalUrl}?t=${Date.now()}`
        applied = true
      }
    } catch (error) {
      // 如果是相对路径，尝试转换为绝对路径
      if (!originalUrl.startsWith('http')) {
        const baseUrl = window.location.origin
        fixedUrl = `${baseUrl}${originalUrl.startsWith('/') ? '' : '/'}${originalUrl}`
        applied = true
      }
    }

    if (applied) {
      font.fontUrl = fixedUrl
    }

    return {
      type: 'fontUrl',
      applied: applied,
      original: originalUrl,
      fixed: fixedUrl,
      description: applied ? '修复了字体URL访问问题' : '字体URL无需修复'
    }
  }

  /**
   * 强制重新加载字体
   */
  async forceReloadFont(font) {
    try {
      // 移除现有的字体样式
      const existingStyles = document.querySelectorAll(`style[id="font-${font.id}"]`)
      existingStyles.forEach(style => style.remove())

      // 从document.fonts中移除
      const cleanFamily = font.family.replace(/['"]/g, '')
      const existingFontFaces = Array.from(document.fonts).filter(f => f.family === cleanFamily)
      existingFontFaces.forEach(fontFace => {
        document.fonts.delete(fontFace)
      })

      // 重新加载字体
      const fontFace = new FontFace(cleanFamily, `url(${font.fontUrl})`)
      await fontFace.load()
      document.fonts.add(fontFace)

      return {
        type: 'forceReload',
        applied: true,
        description: '强制重新加载了字体'
      }
    } catch (error) {
      return {
        type: 'forceReload',
        applied: false,
        error: error.message,
        description: '强制重新加载字体失败'
      }
    }
  }

  /**
   * 清除字体缓存
   */
  clearFontCache(font) {
    try {
      // 清除浏览器字体缓存的方法有限，主要是移除已加载的字体
      const cleanFamily = font.family.replace(/['"]/g, '')
      
      // 移除所有相关的style标签
      const styles = document.querySelectorAll('style')
      styles.forEach(style => {
        if (style.textContent && style.textContent.includes(cleanFamily)) {
          style.remove()
        }
      })

      return {
        type: 'clearCache',
        applied: true,
        description: '清除了字体缓存'
      }
    } catch (error) {
      return {
        type: 'clearCache',
        applied: false,
        error: error.message,
        description: '清除字体缓存失败'
      }
    }
  }

  /**
   * 验证字体修复结果
   */
  async verifyFontFix(font) {
    try {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.style.position = 'absolute'
      testElement.style.left = '-9999px'
      testElement.style.top = '-9999px'
      testElement.style.fontSize = '72px'
      testElement.style.fontFamily = 'monospace'
      testElement.textContent = 'Test Font Verification'
      document.body.appendChild(testElement)

      const defaultWidth = testElement.offsetWidth

      // 应用修复后的字体
      testElement.style.fontFamily = font.family
      
      // 等待字体应用
      await new Promise(resolve => setTimeout(resolve, 200))

      const newWidth = testElement.offsetWidth
      document.body.removeChild(testElement)

      const success = newWidth !== defaultWidth

      return {
        success: success,
        defaultWidth: defaultWidth,
        newWidth: newWidth,
        description: success ? '字体修复验证成功' : '字体修复验证失败'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        description: '字体修复验证异常'
      }
    }
  }

  /**
   * 获取修复历史
   */
  getFixHistory() {
    return this.fixHistory
  }

  /**
   * 清除修复历史
   */
  clearFixHistory() {
    this.fixHistory = []
  }

  /**
   * 生成修复报告
   */
  generateFixReport(fixResult) {
    const report = {
      summary: {
        fontName: fixResult.font.name,
        success: fixResult.success,
        fixesApplied: fixResult.fixes ? fixResult.fixes.filter(f => f.applied).length : 0,
        totalFixes: fixResult.fixes ? fixResult.fixes.length : 0
      },
      details: fixResult.fixes || [],
      verification: fixResult.verification || {},
      recommendations: this.generateRecommendations(fixResult)
    }

    return report
  }

  /**
   * 生成修复建议
   */
  generateRecommendations(fixResult) {
    const recommendations = []

    if (!fixResult.success) {
      recommendations.push('建议检查字体文件是否损坏或格式不支持')
      recommendations.push('尝试使用不同格式的字体文件（如.woff2代替.ttf）')
      recommendations.push('检查服务器字体文件访问权限')
    }

    if (fixResult.fixes) {
      const urlFix = fixResult.fixes.find(f => f.type === 'fontUrl' && !f.applied)
      if (urlFix) {
        recommendations.push('检查字体文件URL是否正确，确保文件存在')
      }

      const familyFix = fixResult.fixes.find(f => f.type === 'fontFamily' && f.applied)
      if (familyFix) {
        recommendations.push('字体族名称已修复，建议更新字体配置')
      }
    }

    return recommendations
  }
}

// 创建全局修复器实例
export const fontFixer = new FontFixer()

// 便捷方法
export async function fixFontIssues(font) {
  const result = await fontFixer.autoFix(font)
  const report = fontFixer.generateFixReport(result)
  
  console.group(`🔧 字体修复报告: ${font.name}`)
  console.log('修复结果:', result.success ? '✅ 成功' : '❌ 失败')
  console.log('应用的修复:', report.summary.fixesApplied)
  console.log('修复详情:', report.details)
  if (report.recommendations.length > 0) {
    console.log('建议:', report.recommendations)
  }
  console.groupEnd()
  
  return { result, report }
}
