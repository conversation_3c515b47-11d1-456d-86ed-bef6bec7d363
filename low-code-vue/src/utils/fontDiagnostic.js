/**
 * 字体诊断工具
 * 用于诊断字体加载和显示问题
 */

export class FontDiagnostic {
  constructor() {
    this.testResults = []
  }

  /**
   * 运行完整的字体诊断
   * @param {Object} font - 字体对象
   * @returns {Object} 诊断结果
   */
  async runDiagnosis(font) {
    console.log('🔍 开始字体诊断:', font.name)
    
    const results = {
      font: font,
      timestamp: new Date().toISOString(),
      tests: {}
    }

    // 1. 检查字体URL可访问性
    results.tests.urlAccessibility = await this.testUrlAccessibility(font)
    
    // 2. 检查字体文件格式
    results.tests.fileFormat = await this.testFileFormat(font)
    
    // 3. 检查字体族名称
    results.tests.fontFamily = this.testFontFamily(font)
    
    // 4. 检查字体加载状态
    results.tests.loadStatus = await this.testFontLoadStatus(font)
    
    // 5. 检查浏览器支持
    results.tests.browserSupport = this.testBrowserSupport()
    
    // 6. 检查CSS应用
    results.tests.cssApplication = await this.testCSSApplication(font)

    // 生成诊断报告
    results.summary = this.generateSummary(results.tests)
    
    console.log('📊 字体诊断完成:', results)
    return results
  }

  /**
   * 测试字体URL可访问性
   */
  async testUrlAccessibility(font) {
    if (!font.fontUrl) {
      return {
        passed: false,
        message: '字体URL为空',
        details: 'fontUrl字段为空或未定义'
      }
    }

    try {
      console.log('🌐 测试字体URL可访问性:', font.fontUrl)

      // 首先尝试HEAD请求
      let response
      try {
        response = await fetch(font.fontUrl, {
          method: 'HEAD',
          cache: 'no-cache',
          mode: 'cors'
        })
      } catch (headError) {
        console.log('HEAD请求失败，尝试GET请求:', headError.message)
        // 如果HEAD请求失败，尝试GET请求
        response = await fetch(font.fontUrl, {
          method: 'GET',
          cache: 'no-cache',
          mode: 'cors'
        })
      }

      if (response.ok) {
        const contentType = response.headers.get('content-type')
        const contentLength = response.headers.get('content-length')
        const corsHeaders = {
          'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
          'access-control-allow-methods': response.headers.get('access-control-allow-methods')
        }

        return {
          passed: true,
          message: '字体文件可访问',
          details: {
            status: response.status,
            contentType: contentType,
            contentLength: contentLength,
            corsHeaders: corsHeaders,
            url: font.fontUrl,
            finalUrl: response.url
          }
        }
      } else {
        return {
          passed: false,
          message: `字体文件访问失败: ${response.status} ${response.statusText}`,
          details: {
            status: response.status,
            statusText: response.statusText,
            url: font.fontUrl,
            finalUrl: response.url,
            headers: Object.fromEntries(response.headers.entries())
          }
        }
      }
    } catch (error) {
      // 分析错误类型
      let errorType = 'unknown'
      let suggestion = ''

      if (error.name === 'TypeError' && error.message.includes('CORS')) {
        errorType = 'cors'
        suggestion = '跨域访问被阻止，请检查服务器CORS配置'
      } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        errorType = 'network'
        suggestion = '网络请求失败，请检查URL是否正确或服务器是否运行'
      } else if (error.message.includes('404')) {
        errorType = 'notfound'
        suggestion = '字体文件不存在，请检查文件路径'
      }

      return {
        passed: false,
        message: '字体URL访问异常',
        details: {
          error: error.message,
          errorType: errorType,
          suggestion: suggestion,
          url: font.fontUrl,
          stack: error.stack
        }
      }
    }
  }

  /**
   * 测试字体文件格式
   */
  async testFileFormat(font) {
    if (!font.fontUrl) {
      return {
        passed: false,
        message: '无法检测文件格式：URL为空'
      }
    }

    const url = font.fontUrl.toLowerCase()
    const supportedFormats = ['.ttf', '.otf', '.woff', '.woff2']
    const detectedFormat = supportedFormats.find(format => url.includes(format))

    if (detectedFormat) {
      return {
        passed: true,
        message: `检测到支持的字体格式: ${detectedFormat}`,
        details: {
          format: detectedFormat,
          url: font.fontUrl
        }
      }
    } else {
      return {
        passed: false,
        message: '未检测到支持的字体格式',
        details: {
          url: font.fontUrl,
          supportedFormats: supportedFormats
        }
      }
    }
  }

  /**
   * 测试字体族名称
   */
  testFontFamily(font) {
    if (!font.family) {
      return {
        passed: false,
        message: '字体族名称为空'
      }
    }

    const family = font.family.trim()
    const hasQuotes = family.includes('"') || family.includes("'")
    const cleanFamily = family.replace(/['"]/g, '')

    return {
      passed: true,
      message: '字体族名称格式正确',
      details: {
        original: font.family,
        cleaned: cleanFamily,
        hasQuotes: hasQuotes
      }
    }
  }

  /**
   * 测试字体加载状态
   */
  async testFontLoadStatus(font) {
    if (!font.fontUrl) {
      return {
        passed: false,
        message: '无法测试加载状态：URL为空'
      }
    }

    try {
      const cleanFamily = font.family.replace(/['"]/g, '')
      
      // 检查字体是否已在document.fonts中
      const fontFaces = Array.from(document.fonts)
      const existingFont = fontFaces.find(f => f.family === cleanFamily)
      
      if (existingFont) {
        return {
          passed: true,
          message: '字体已加载到document.fonts',
          details: {
            fontFace: {
              family: existingFont.family,
              status: existingFont.status,
              loaded: existingFont.loaded
            }
          }
        }
      }

      // 尝试加载字体
      const fontFace = new FontFace(cleanFamily, `url(${font.fontUrl})`)
      await fontFace.load()
      
      return {
        passed: true,
        message: '字体可以成功加载',
        details: {
          family: cleanFamily,
          status: fontFace.status
        }
      }
    } catch (error) {
      return {
        passed: false,
        message: '字体加载失败',
        details: {
          error: error.message,
          family: font.family
        }
      }
    }
  }

  /**
   * 测试浏览器支持
   */
  testBrowserSupport() {
    const support = {
      fontFaceAPI: 'FontFace' in window,
      documentFonts: 'fonts' in document,
      fontDisplay: CSS && CSS.supports && CSS.supports('font-display', 'swap'),
      fetch: 'fetch' in window,
      promises: 'Promise' in window,
      cssSupports: 'CSS' in window && 'supports' in CSS
    }

    const criticalSupport = support.fontFaceAPI && support.documentFonts && support.fetch
    const allSupported = Object.values(support).every(Boolean)

    let message = ''
    if (allSupported) {
      message = '浏览器完全支持字体API'
    } else if (criticalSupport) {
      message = '浏览器支持核心字体API，部分功能可能受限'
    } else {
      message = '浏览器不支持关键字体API，字体功能可能无法正常工作'
    }

    return {
      passed: criticalSupport,
      message: message,
      details: {
        ...support,
        userAgent: navigator.userAgent,
        browserInfo: this.getBrowserInfo()
      }
    }
  }

  /**
   * 获取浏览器信息
   */
  getBrowserInfo() {
    const ua = navigator.userAgent
    let browser = 'Unknown'
    let version = 'Unknown'

    if (ua.includes('Chrome')) {
      browser = 'Chrome'
      const match = ua.match(/Chrome\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Firefox')) {
      browser = 'Firefox'
      const match = ua.match(/Firefox\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
      browser = 'Safari'
      const match = ua.match(/Version\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Edge')) {
      browser = 'Edge'
      const match = ua.match(/Edge\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    }

    return {
      name: browser,
      version: version,
      platform: navigator.platform
    }
  }

  /**
   * 测试CSS应用
   */
  async testCSSApplication(font) {
    if (!font.family) {
      return {
        passed: false,
        message: '无法测试CSS应用：字体族为空'
      }
    }

    try {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.style.position = 'absolute'
      testElement.style.left = '-9999px'
      testElement.style.top = '-9999px'
      testElement.style.fontSize = '72px'
      testElement.style.fontFamily = 'monospace'
      testElement.textContent = 'Test Font 测试字体 123'
      document.body.appendChild(testElement)

      const defaultWidth = testElement.offsetWidth
      const defaultHeight = testElement.offsetHeight

      // 应用目标字体
      testElement.style.fontFamily = font.family
      
      // 等待一段时间让字体应用
      await new Promise(resolve => setTimeout(resolve, 100))

      const newWidth = testElement.offsetWidth
      const newHeight = testElement.offsetHeight

      document.body.removeChild(testElement)

      const dimensionsChanged = newWidth !== defaultWidth || newHeight !== defaultHeight

      return {
        passed: dimensionsChanged,
        message: dimensionsChanged ? '字体CSS应用成功，文本尺寸发生变化' : '字体CSS应用可能失败，文本尺寸未变化',
        details: {
          defaultDimensions: { width: defaultWidth, height: defaultHeight },
          newDimensions: { width: newWidth, height: newHeight },
          changed: dimensionsChanged
        }
      }
    } catch (error) {
      return {
        passed: false,
        message: 'CSS应用测试失败',
        details: {
          error: error.message
        }
      }
    }
  }

  /**
   * 生成诊断摘要
   */
  generateSummary(tests) {
    const totalTests = Object.keys(tests).length
    const passedTests = Object.values(tests).filter(test => test.passed).length
    const failedTests = totalTests - passedTests

    const issues = Object.entries(tests)
      .filter(([key, test]) => !test.passed)
      .map(([key, test]) => ({
        test: key,
        message: test.message,
        details: test.details
      }))

    return {
      totalTests,
      passedTests,
      failedTests,
      success: failedTests === 0,
      issues
    }
  }

  /**
   * 打印诊断报告
   */
  printReport(results) {
    console.group(`📋 字体诊断报告: ${results.font.name}`)
    
    console.log(`⏰ 诊断时间: ${results.timestamp}`)
    console.log(`📊 测试结果: ${results.summary.passedTests}/${results.summary.totalTests} 通过`)
    
    if (results.summary.success) {
      console.log('✅ 所有测试通过，字体应该可以正常工作')
    } else {
      console.log('❌ 发现问题，需要修复:')
      results.summary.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.test}: ${issue.message}`)
        if (issue.details) {
          console.log('   详情:', issue.details)
        }
      })
    }
    
    console.groupEnd()
  }
}

// 创建全局诊断实例
export const fontDiagnostic = new FontDiagnostic()

// 便捷方法
export async function diagnoseFontIssues(font) {
  const results = await fontDiagnostic.runDiagnosis(font)
  fontDiagnostic.printReport(results)
  return results
}
