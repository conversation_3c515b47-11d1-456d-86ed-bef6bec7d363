<template>
  <div class="font-selector">
    <el-select
      v-model="selectedFontId"
      @change="handleFontChange"
      placeholder="选择字体"
      size="small"
      style="width: 100%"
      filterable
    >
      <el-option-group
        v-for="group in fontGroups"
        :key="group.label"
        :label="group.label"
      >
        <el-option
          v-for="font in group.fonts"
          :key="font.id"
          :label="font.name"
          :value="font.id"
          :style="{ fontFamily: font.family }"
        >
          <div class="font-option">
            <span class="font-name">{{ font.name }}</span>
            <span class="font-preview" :style="{ fontFamily: font.family }">
              {{ font.preview }}
            </span>
          </div>
        </el-option>
      </el-option-group>
    </el-select>
    
    <div class="font-actions">
      <el-button 
        size="small" 
        icon="Setting" 
        @click="openFontManager"
        title="字体管理"
        circle
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useEditorStore } from '../../store'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'openFontManager'])

const editorStore = useEditorStore()

// 当前选中的字体ID
const selectedFontId = ref('')

// 字体分组
const fontGroups = computed(() => {
  const groups = [
    {
      label: '系统字体',
      fonts: editorStore.fontLibrary.filter(font => font.type === 'system')
    },
    {
      label: 'Web字体',
      fonts: editorStore.fontLibrary.filter(font => font.type === 'web')
    },
    {
      label: '自定义字体',
      fonts: editorStore.fontLibrary.filter(font => font.type === 'custom')
    }
  ]
  
  // 过滤掉空的分组
  return groups.filter(group => group.fonts.length > 0)
})

// 根据字体family查找字体ID
const findFontIdByFamily = (fontFamily) => {
  if (!fontFamily) return ''
  
  const font = editorStore.fontLibrary.find(f => f.family === fontFamily)
  return font ? font.id : ''
}

// 初始化选中的字体
const initSelectedFont = () => {
  if (props.modelValue) {
    selectedFontId.value = findFontIdByFamily(props.modelValue)
  }
}

// 处理字体变化
const handleFontChange = async (fontId) => {
  const font = editorStore.fontLibrary.find(f => f.id === fontId)
  if (!font) return

  try {
    console.log('字体选择变化:', font.name, '类型:', font.type, '字体族:', font.family)

    // 如果是Web字体或自定义字体，先加载
    if (font.type === 'web' || font.type === 'custom') {
      ElMessage.info(`正在加载字体: ${font.name}`)

      const loaded = await editorStore.loadWebFont(font)
      if (!loaded) {
        ElMessage.error(`字体 ${font.name} 加载失败，请检查字体文件是否可访问`)
        return
      }

      ElMessage.success(`字体 ${font.name} 加载成功`)
    }

    // 更新字体
    console.log('应用字体族:', font.family)
    emit('update:modelValue', font.family)

    // 强制刷新预览
    await nextTick()
    refreshFontPreview()

  } catch (error) {
    console.error('字体加载错误:', error)
    ElMessage.error(`字体加载失败: ${error.message || '未知错误'}`)
  }
}

// 刷新字体预览
const refreshFontPreview = () => {
  // 强制重新渲染字体预览
  const fontOptions = document.querySelectorAll('.font-option .font-preview')
  fontOptions.forEach(element => {
    const fontFamily = element.style.fontFamily
    if (fontFamily) {
      element.style.fontFamily = ''
      setTimeout(() => {
        element.style.fontFamily = fontFamily
      }, 10)
    }
  })
}

// 打开字体管理器
const openFontManager = () => {
  emit('openFontManager')
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedFontId.value = findFontIdByFamily(newValue)
}, { immediate: true })

// 初始化
initSelectedFont()
</script>

<style scoped>
.font-selector {
  display: flex;
  gap: 8px;
  align-items: center;
}

.font-actions {
  flex-shrink: 0;
}

.font-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.font-name {
  flex: 1;
  text-align: left;
}

.font-preview {
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  margin-left: 10px;
}

/* 确保下拉选项中的字体预览正确显示 */
:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
}

:deep(.el-select-dropdown__item .font-option) {
  min-height: 24px;
}
</style>
