# 字体管理本地上传功能使用指南

## 🎯 功能简介

字体管理功能现已支持本地字体文件上传！您可以上传自己的字体文件到系统中，并在页面编辑器中使用这些字体。

## 📁 支持的字体格式

- ✅ `.ttf` - TrueType字体
- ✅ `.otf` - OpenType字体  
- ✅ `.woff` - Web开放字体格式
- ✅ `.woff2` - Web开放字体格式2.0

**文件大小限制**: 最大10MB

## 🚀 快速开始

### 步骤1: 访问字体管理器

**方法一：通过页面编辑器**
1. 在页面编辑器中选择任意文本组件（文本、标题、段落、按钮等）
2. 在右侧属性面板点击"样式"选项卡
3. 展开"字体"部分
4. 在字体选择器旁边点击设置按钮 ⚙️

**方法二：直接测试**
- 访问 `/font-test` 页面进行功能测试

### 步骤2: 添加本地字体

1. 点击"添加字体"按钮
2. 填写字体信息：
   - **字体名称**: 例如"我的自定义字体"
   - **字体类型**: 选择"自定义字体"
   - **字体文件**: 拖拽字体文件到上传区域，或点击选择文件
   - **预览文本**: 可自定义预览文本（默认为"Aa"）

3. 系统会自动填充字体族名称
4. 点击"确定"保存

### 步骤3: 使用自定义字体

1. 在字体选择器中找到"自定义字体"分组
2. 选择您刚上传的字体
3. 系统会自动加载字体文件
4. 字体加载成功后即可在页面中看到效果

## 💡 使用技巧

### 字体命名建议
- 使用有意义的名称，如"标题专用字体"、"品牌字体"等
- 避免使用特殊字符和空格

### 字体文件选择
- 优先选择 `.woff2` 格式（文件更小，加载更快）
- 如需兼容老版本浏览器，可选择 `.ttf` 格式

### 性能优化
- 避免上传过大的字体文件
- 只上传项目中确实需要的字体
- 定期清理不再使用的字体

## 🔧 功能特性

### 字体类型管理
- **系统字体**: 预装的系统字体（Arial、宋体等）
- **Web字体**: 在线字体（Google Fonts等）
- **自定义字体**: 您上传的本地字体文件

### 字体状态显示
- **可用**: 系统字体，无需加载
- **未加载**: Web字体和自定义字体的初始状态
- **已加载**: 字体文件已成功加载到浏览器

### 字体管理操作
- **加载**: 手动加载Web字体或自定义字体
- **编辑**: 修改自定义字体的信息
- **删除**: 删除不需要的自定义字体

## 🛠️ 故障排除

### 上传失败
**可能原因**:
- 文件格式不支持
- 文件大小超过10MB限制
- 网络连接问题

**解决方法**:
- 检查文件格式是否为支持的类型
- 压缩字体文件或选择更小的文件
- 检查网络连接并重试

### 字体加载失败
**可能原因**:
- 字体文件损坏
- 服务器访问问题
- 浏览器兼容性问题

**解决方法**:
- 重新上传字体文件
- 刷新页面重试
- 尝试使用不同格式的字体文件

### 字体显示异常
**可能原因**:
- 字体未正确加载
- CSS样式冲突
- 浏览器缓存问题

**解决方法**:
- 确认字体状态为"已加载"
- 清除浏览器缓存
- 检查是否有其他CSS样式覆盖

## 📝 注意事项

1. **版权问题**: 确保您有权使用上传的字体文件
2. **浏览器兼容性**: 不同浏览器对字体格式支持可能不同
3. **加载性能**: 过多的自定义字体可能影响页面加载速度
4. **文件管理**: 定期清理不再使用的字体文件

## 🎨 最佳实践

1. **字体选择**: 选择与项目风格匹配的字体
2. **数量控制**: 每个项目建议不超过3-5个自定义字体
3. **格式优化**: 优先使用压缩格式（.woff2）
4. **预览测试**: 上传后及时测试字体在不同设备上的显示效果

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 尝试使用不同的字体文件
3. 联系技术支持团队

---

**祝您使用愉快！** 🎉
