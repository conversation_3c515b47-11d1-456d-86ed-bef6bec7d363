package com.web.lowcode.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * 字体文件静态资源配置
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Configuration
public class FontResourceConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置字体文件访问路径
        String fontPath = uploadPath + File.separator + "font" + File.separator;
        
        registry.addResourceHandler("/fonts/**")
                .addResourceLocations("file:" + fontPath)
                .setCachePeriod(3600 * 24 * 30) // 缓存30天
                .resourceChain(true);
        
        // 添加CORS支持
        registry.addResourceHandler("/api/fonts/file/**")
                .addResourceLocations("file:" + fontPath)
                .setCachePeriod(3600 * 24 * 30) // 缓存30天
                .resourceChain(true);
    }
}
