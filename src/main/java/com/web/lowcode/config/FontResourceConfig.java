package com.web.lowcode.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * 字体文件静态资源配置
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Configuration
public class FontResourceConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 注意：字体文件访问通过FontController处理，不需要静态资源映射
        // 这里只保留注释说明，实际的字体文件访问由 /api/fonts/file/{fileName} 控制器处理

        // 如果需要其他静态资源映射，可以在这里添加
        // 但字体文件必须通过控制器处理以确保CORS和安全性
    }
}
