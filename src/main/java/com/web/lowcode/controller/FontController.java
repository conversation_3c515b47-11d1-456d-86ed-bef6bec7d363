package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.Font;
import com.web.lowcode.service.FontService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * 字体管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Slf4j
@RestController
@RequestMapping("/api/fonts")
@RequiredArgsConstructor
@Tag(name = "字体管理", description = "字体文件上传、管理和访问接口")
public class FontController {

    private final FontService fontService;

    @PostMapping("/upload")
    @Operation(summary = "上传字体文件", description = "上传自定义字体文件")
    public Result<Font> uploadFont(
            @Parameter(description = "字体文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "字体名称") @RequestParam("name") String name,
            @Parameter(description = "字体族") @RequestParam("family") String family,
            @Parameter(description = "字体描述") @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "预览文本") @RequestParam(value = "previewText", required = false) String previewText) {
        
        try {
            Font font = fontService.uploadFont(file, name, family, description, previewText);
            return Result.success(font);
        } catch (Exception e) {
            log.error("字体上传失败", e);
            return Result.error("字体上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/web")
    @Operation(summary = "添加Web字体", description = "添加在线Web字体")
    public Result<Font> addWebFont(
            @Parameter(description = "字体名称") @RequestParam("name") String name,
            @Parameter(description = "字体族") @RequestParam("family") String family,
            @Parameter(description = "CSS URL") @RequestParam("cssUrl") String cssUrl,
            @Parameter(description = "字体描述") @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "预览文本") @RequestParam(value = "previewText", required = false) String previewText) {
        
        try {
            Font font = fontService.addWebFont(name, family, cssUrl, description, previewText);
            return Result.success(font);
        } catch (Exception e) {
            log.error("Web字体添加失败", e);
            return Result.error("Web字体添加失败: " + e.getMessage());
        }
    }

    @PostMapping("/system")
    @Operation(summary = "添加系统字体", description = "添加系统预装字体")
    public Result<Font> addSystemFont(
            @Parameter(description = "字体名称") @RequestParam("name") String name,
            @Parameter(description = "字体族") @RequestParam("family") String family,
            @Parameter(description = "字体描述") @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "预览文本") @RequestParam(value = "previewText", required = false) String previewText) {
        
        try {
            Font font = fontService.addSystemFont(name, family, description, previewText);
            return Result.success(font);
        } catch (Exception e) {
            log.error("系统字体添加失败", e);
            return Result.error("系统字体添加失败: " + e.getMessage());
        }
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询字体", description = "分页查询字体列表")
    public Result<IPage<Font>> getFontPage(
            @Parameter(description = "当前页") @RequestParam(value = "current", defaultValue = "1") Long current,
            @Parameter(description = "页大小") @RequestParam(value = "size", defaultValue = "10") Long size,
            @Parameter(description = "字体名称") @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "字体类型") @RequestParam(value = "type", required = false) String type,
            @Parameter(description = "是否启用") @RequestParam(value = "enabled", required = false) Integer enabled) {
        
        try {
            IPage<Font> page = fontService.getFontPage(current, size, name, type, enabled);
            return Result.success(page);
        } catch (Exception e) {
            log.error("查询字体列表失败", e);
            return Result.error("查询字体列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询启用字体", description = "查询所有启用的字体")
    public Result<List<Font>> getEnabledFonts() {
        try {
            List<Font> fonts = fontService.getEnabledFonts();
            return Result.success(fonts);
        } catch (Exception e) {
            log.error("查询启用字体失败", e);
            return Result.error("查询启用字体失败: " + e.getMessage());
        }
    }

    @GetMapping("/type/{type}")
    @Operation(summary = "按类型查询字体", description = "根据字体类型查询字体列表")
    public Result<List<Font>> getFontsByType(
            @Parameter(description = "字体类型") @PathVariable("type") String type) {
        
        try {
            List<Font> fonts = fontService.getFontsByType(type);
            return Result.success(fonts);
        } catch (Exception e) {
            log.error("按类型查询字体失败", e);
            return Result.error("按类型查询字体失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询字体详情", description = "根据ID查询字体详情")
    public Result<Font> getFontById(@Parameter(description = "字体ID") @PathVariable("id") Long id) {
        try {
            Font font = fontService.getById(id);
            if (font == null) {
                return Result.error("字体不存在");
            }
            return Result.success(font);
        } catch (Exception e) {
            log.error("查询字体详情失败", e);
            return Result.error("查询字体详情失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新字体信息", description = "更新字体基本信息")
    public Result<Font> updateFont(
            @Parameter(description = "字体ID") @PathVariable("id") Long id,
            @Parameter(description = "字体名称") @RequestParam("name") String name,
            @Parameter(description = "字体族") @RequestParam("family") String family,
            @Parameter(description = "CSS URL") @RequestParam(value = "cssUrl", required = false) String cssUrl,
            @Parameter(description = "字体描述") @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "预览文本") @RequestParam(value = "previewText", required = false) String previewText) {
        
        try {
            Font font = fontService.updateFont(id, name, family, cssUrl, description, previewText);
            return Result.success(font);
        } catch (Exception e) {
            log.error("字体更新失败", e);
            return Result.error("字体更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除字体", description = "删除指定字体")
    public Result<Void> deleteFont(@Parameter(description = "字体ID") @PathVariable("id") Long id) {
        try {
            boolean success = fontService.deleteFont(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("字体删除失败");
            }
        } catch (Exception e) {
            log.error("字体删除失败", e);
            return Result.error("字体删除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除字体", description = "批量删除指定字体")
    public Result<Void> deleteFonts(@Parameter(description = "字体ID列表") @RequestBody List<Long> ids) {
        try {
            boolean success = fontService.deleteFonts(ids);
            if (success) {
                return Result.success();
            } else {
                return Result.error("字体批量删除失败");
            }
        } catch (Exception e) {
            log.error("字体批量删除失败", e);
            return Result.error("字体批量删除失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/enabled")
    @Operation(summary = "更新字体状态", description = "启用或禁用字体")
    public Result<Void> updateEnabledStatus(
            @Parameter(description = "字体ID") @PathVariable("id") Long id,
            @Parameter(description = "启用状态") @RequestParam("enabled") Integer enabled) {

        try {
            boolean success = fontService.updateEnabledStatus(id, enabled);
            if (success) {
                return Result.success();
            } else {
                return Result.error("字体状态更新失败");
            }
        } catch (Exception e) {
            log.error("字体状态更新失败", e);
            return Result.error("字体状态更新失败: " + e.getMessage());
        }
    }

    @PostMapping("/fix-urls")
    @Operation(summary = "修复字体URL", description = "修复现有字体的URL格式")
    public Result<String> fixFontUrls() {
        try {
            fontService.fixExistingFontUrls();
            return Result.success("字体URL修复完成");
        } catch (Exception e) {
            log.error("字体URL修复失败", e);
            return Result.error("字体URL修复失败: " + e.getMessage());
        }
    }

    @GetMapping("/file/{fileName}")
    @Operation(summary = "访问字体文件", description = "通过文件名访问字体文件")
    public ResponseEntity<Resource> getFontFile(
            @Parameter(description = "文件名") @PathVariable("fileName") String fileName) {
        
        try {
            // 构建文件路径 - 使用绝对路径
            String fontDirPath = System.getProperty("user.dir") + File.separator + "uploads" + File.separator + "font";
            File file = new File(fontDirPath, fileName);

            log.info("尝试访问字体文件: {}", file.getAbsolutePath());

            if (!file.exists()) {
                log.warn("字体文件不存在: {}", file.getAbsolutePath());
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName);

            // 添加CORS支持
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS");
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");

            // 根据文件扩展名设置Content-Type
            String contentType = getContentType(fileName);

            log.info("返回字体文件: {} (Content-Type: {})", fileName, contentType);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("访问字体文件失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "ttf":
                return "font/ttf";
            case "otf":
                return "font/otf";
            case "woff":
                return "font/woff";
            case "woff2":
                return "font/woff2";
            default:
                return "application/octet-stream";
        }
    }
}
