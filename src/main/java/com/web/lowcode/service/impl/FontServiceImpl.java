package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.entity.Font;
import com.web.lowcode.mapper.FontMapper;
import com.web.lowcode.service.FontService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 字体管理服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Slf4j
@Service
public class FontServiceImpl extends ServiceImpl<FontMapper, Font> implements FontService {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${server.port:8080}")
    private String serverPort;

    private static final String FONT_DIR = "font";
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final String[] ALLOWED_EXTENSIONS = {".ttf", ".otf", ".woff", ".woff2"};

    @Override
    public Font uploadFont(MultipartFile file, String name, String family, 
                          String description, String previewText) {
        try {
            // 验证文件
            validateFontFile(file);
            
            // 检查名称和字体族是否重复
            if (checkNameExists(name, null)) {
                throw new RuntimeException("字体名称已存在: " + name);
            }
            if (checkFamilyExists(family, null)) {
                throw new RuntimeException("字体族已存在: " + family);
            }
            
            // 创建字体目录
            String fontDirPath = uploadPath + File.separator + FONT_DIR;
            File fontDir = new File(fontDirPath);
            if (!fontDir.exists()) {
                fontDir.mkdirs();
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String fileName = generateFileName() + fileExtension;
            
            // 保存文件
            Path filePath = Paths.get(fontDirPath, fileName);
            Files.copy(file.getInputStream(), filePath);
            
            // 创建字体记录
            Font font = new Font();
            font.setName(name);
            font.setFamily(family);
            font.setType("custom");
            font.setOriginalName(originalFilename);
            font.setFileName(fileName);
            font.setFileSize(file.getSize());
            font.setFileFormat(fileExtension.substring(1)); // 去掉点号
            font.setFontUrl(getFontFileUrl(fileName));
            font.setDescription(description);
            font.setPreviewText(StringUtils.hasText(previewText) ? previewText : "Aa");
            font.setEnabled(1);
            font.setSortOrder(0);
            
            // 保存到数据库
            save(font);
            
            log.info("字体文件上传成功: {} -> {}", originalFilename, fileName);
            return font;
            
        } catch (IOException e) {
            log.error("字体文件上传失败", e);
            throw new RuntimeException("字体文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<Font> getFontPage(Long current, Long size, String name, String type, Integer enabled) {
        Page<Font> page = new Page<>(current, size);
        return baseMapper.selectFontPage(page, name, type, enabled);
    }

    @Override
    public List<Font> getEnabledFonts() {
        return baseMapper.selectEnabledFonts();
    }

    @Override
    public List<Font> getFontsByType(String type) {
        return baseMapper.selectFontsByType(type);
    }

    @Override
    public Font addWebFont(String name, String family, String cssUrl, 
                          String description, String previewText) {
        // 检查名称和字体族是否重复
        if (checkNameExists(name, null)) {
            throw new RuntimeException("字体名称已存在: " + name);
        }
        if (checkFamilyExists(family, null)) {
            throw new RuntimeException("字体族已存在: " + family);
        }
        
        Font font = new Font();
        font.setName(name);
        font.setFamily(family);
        font.setType("web");
        font.setCssUrl(cssUrl);
        font.setDescription(description);
        font.setPreviewText(StringUtils.hasText(previewText) ? previewText : "Aa");
        font.setEnabled(1);
        font.setSortOrder(0);
        
        save(font);
        return font;
    }

    @Override
    public Font addSystemFont(String name, String family, String description, String previewText) {
        // 检查名称和字体族是否重复
        if (checkNameExists(name, null)) {
            throw new RuntimeException("字体名称已存在: " + name);
        }
        if (checkFamilyExists(family, null)) {
            throw new RuntimeException("字体族已存在: " + family);
        }
        
        Font font = new Font();
        font.setName(name);
        font.setFamily(family);
        font.setType("system");
        font.setDescription(description);
        font.setPreviewText(StringUtils.hasText(previewText) ? previewText : "Aa");
        font.setEnabled(1);
        font.setSortOrder(0);
        
        save(font);
        return font;
    }

    @Override
    public Font updateFont(Long id, String name, String family, String cssUrl, 
                          String description, String previewText) {
        Font font = getById(id);
        if (font == null) {
            throw new RuntimeException("字体不存在");
        }
        
        // 检查名称和字体族是否重复
        if (checkNameExists(name, id)) {
            throw new RuntimeException("字体名称已存在: " + name);
        }
        if (checkFamilyExists(family, id)) {
            throw new RuntimeException("字体族已存在: " + family);
        }
        
        font.setName(name);
        font.setFamily(family);
        if ("web".equals(font.getType())) {
            font.setCssUrl(cssUrl);
        }
        font.setDescription(description);
        font.setPreviewText(StringUtils.hasText(previewText) ? previewText : "Aa");
        
        updateById(font);
        return font;
    }

    @Override
    public boolean deleteFont(Long id) {
        Font font = getById(id);
        if (font == null) {
            return false;
        }
        
        // 如果是自定义字体，删除文件
        if ("custom".equals(font.getType()) && StringUtils.hasText(font.getFileName())) {
            deleteFontFile(font.getFileName());
        }
        
        return removeById(id);
    }

    @Override
    public boolean deleteFonts(List<Long> ids) {
        List<Font> fonts = listByIds(ids);
        
        // 删除自定义字体文件
        for (Font font : fonts) {
            if ("custom".equals(font.getType()) && StringUtils.hasText(font.getFileName())) {
                deleteFontFile(font.getFileName());
            }
        }
        
        return removeByIds(ids);
    }

    @Override
    public boolean updateEnabledStatus(Long id, Integer enabled) {
        return baseMapper.updateEnabledStatus(id, enabled) > 0;
    }

    @Override
    public boolean batchUpdateSortOrder(List<Font> fonts) {
        return baseMapper.batchUpdateSortOrder(fonts) > 0;
    }

    @Override
    public boolean checkNameExists(String name, Long excludeId) {
        return baseMapper.checkNameExists(name, excludeId) > 0;
    }

    @Override
    public boolean checkFamilyExists(String family, Long excludeId) {
        return baseMapper.checkFamilyExists(family, excludeId) > 0;
    }

    @Override
    public String getFontFileUrl(String fileName) {
        // 修复URL路径，确保与控制器映射一致
        return String.format("http://localhost:%s%s/api/fonts/file/%s",
                           serverPort, contextPath, fileName);
    }

    /**
     * 修复现有字体的URL格式
     * 将错误的 /fonts/ 路径更新为正确的 /api/fonts/file/ 路径
     */
    public void fixExistingFontUrls() {
        log.info("开始修复现有字体URL格式...");

        // 获取所有自定义字体
        List<Font> customFonts = list(new QueryWrapper<Font>()
                .eq("type", "custom")
                .isNotNull("font_url"));

        int fixedCount = 0;
        for (Font font : customFonts) {
            String oldUrl = font.getFontUrl();

            // 检查是否需要修复
            if (oldUrl != null && oldUrl.contains("/fonts/") && !oldUrl.contains("/api/fonts/file/")) {
                // 修复URL
                String newUrl = oldUrl.replace("/fonts/", "/api/fonts/file/");
                font.setFontUrl(newUrl);

                // 更新数据库
                updateById(font);
                fixedCount++;

                log.info("修复字体URL: {} -> {}", oldUrl, newUrl);
            }
        }

        log.info("字体URL修复完成，共修复 {} 个字体", fixedCount);
    }

    /**
     * 验证字体文件
     */
    private void validateFontFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("请选择字体文件");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("字体文件大小不能超过10MB");
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new RuntimeException("文件名不能为空");
        }
        
        String extension = getFileExtension(originalFilename).toLowerCase();
        boolean isValidExtension = false;
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equals(extension)) {
                isValidExtension = true;
                break;
            }
        }
        
        if (!isValidExtension) {
            throw new RuntimeException("不支持的字体文件格式，支持的格式: " + String.join(", ", ALLOWED_EXTENSIONS));
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * 生成文件名
     */
    private String generateFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "font_" + timestamp + "_" + uuid;
    }

    /**
     * 删除字体文件
     */
    private void deleteFontFile(String fileName) {
        try {
            String fontDirPath = uploadPath + File.separator + FONT_DIR;
            Path filePath = Paths.get(fontDirPath, fileName);
            Files.deleteIfExists(filePath);
            log.info("字体文件删除成功: {}", fileName);
        } catch (IOException e) {
            log.error("字体文件删除失败: {}", fileName, e);
        }
    }
}
