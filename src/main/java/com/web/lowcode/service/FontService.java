package com.web.lowcode.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.Font;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 字体管理服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
public interface FontService extends IService<Font> {

    /**
     * 上传字体文件
     * 
     * @param file 字体文件
     * @param name 字体名称
     * @param family 字体族
     * @param description 字体描述
     * @param previewText 预览文本
     * @return 字体信息
     */
    Font uploadFont(MultipartFile file, String name, String family, 
                   String description, String previewText);

    /**
     * 分页查询字体列表
     * 
     * @param current 当前页
     * @param size 页大小
     * @param name 字体名称 (模糊查询)
     * @param type 字体类型
     * @param enabled 是否启用
     * @return 字体分页列表
     */
    IPage<Font> getFontPage(Long current, Long size, String name, String type, Integer enabled);

    /**
     * 查询启用的字体列表
     * 
     * @return 启用的字体列表
     */
    List<Font> getEnabledFonts();

    /**
     * 根据类型查询字体列表
     * 
     * @param type 字体类型
     * @return 字体列表
     */
    List<Font> getFontsByType(String type);

    /**
     * 添加Web字体
     * 
     * @param name 字体名称
     * @param family 字体族
     * @param cssUrl CSS URL
     * @param description 字体描述
     * @param previewText 预览文本
     * @return 字体信息
     */
    Font addWebFont(String name, String family, String cssUrl, 
                   String description, String previewText);

    /**
     * 添加系统字体
     * 
     * @param name 字体名称
     * @param family 字体族
     * @param description 字体描述
     * @param previewText 预览文本
     * @return 字体信息
     */
    Font addSystemFont(String name, String family, String description, String previewText);

    /**
     * 更新字体信息
     * 
     * @param id 字体ID
     * @param name 字体名称
     * @param family 字体族
     * @param cssUrl CSS URL (Web字体)
     * @param description 字体描述
     * @param previewText 预览文本
     * @return 更新后的字体信息
     */
    Font updateFont(Long id, String name, String family, String cssUrl, 
                   String description, String previewText);

    /**
     * 删除字体
     * 
     * @param id 字体ID
     * @return 是否删除成功
     */
    boolean deleteFont(Long id);

    /**
     * 批量删除字体
     * 
     * @param ids 字体ID列表
     * @return 是否删除成功
     */
    boolean deleteFonts(List<Long> ids);

    /**
     * 更新字体启用状态
     * 
     * @param id 字体ID
     * @param enabled 启用状态
     * @return 是否更新成功
     */
    boolean updateEnabledStatus(Long id, Integer enabled);

    /**
     * 批量更新排序
     * 
     * @param fonts 字体列表
     * @return 是否更新成功
     */
    boolean batchUpdateSortOrder(List<Font> fonts);

    /**
     * 检查字体名称是否存在
     * 
     * @param name 字体名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkNameExists(String name, Long excludeId);

    /**
     * 检查字体族是否存在
     * 
     * @param family 字体族
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkFamilyExists(String family, Long excludeId);

    /**
     * 获取字体文件访问URL
     *
     * @param fileName 文件名
     * @return 访问URL
     */
    String getFontFileUrl(String fileName);

    /**
     * 修复现有字体的URL格式
     * 将错误的 /fonts/ 路径更新为正确的 /api/fonts/file/ 路径
     */
    void fixExistingFontUrls();
}
