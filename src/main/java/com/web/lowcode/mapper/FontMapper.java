package com.web.lowcode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.entity.Font;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字体管理Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Mapper
public interface FontMapper extends BaseMapper<Font> {

    /**
     * 分页查询字体列表
     * 
     * @param page 分页参数
     * @param name 字体名称 (模糊查询)
     * @param type 字体类型
     * @param enabled 是否启用
     * @return 字体分页列表
     */
    IPage<Font> selectFontPage(Page<Font> page, 
                               @Param("name") String name,
                               @Param("type") String type, 
                               @Param("enabled") Integer enabled);

    /**
     * 查询启用的字体列表
     * 
     * @return 启用的字体列表
     */
    List<Font> selectEnabledFonts();

    /**
     * 根据类型查询字体列表
     * 
     * @param type 字体类型
     * @return 字体列表
     */
    List<Font> selectFontsByType(@Param("type") String type);

    /**
     * 检查字体名称是否存在
     * 
     * @param name 字体名称
     * @param excludeId 排除的ID (用于更新时检查)
     * @return 存在的数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 检查字体族是否存在
     * 
     * @param family 字体族
     * @param excludeId 排除的ID (用于更新时检查)
     * @return 存在的数量
     */
    int checkFamilyExists(@Param("family") String family, @Param("excludeId") Long excludeId);

    /**
     * 更新字体启用状态
     * 
     * @param id 字体ID
     * @param enabled 启用状态
     * @return 更新行数
     */
    int updateEnabledStatus(@Param("id") Long id, @Param("enabled") Integer enabled);

    /**
     * 批量更新排序
     * 
     * @param fonts 字体列表
     * @return 更新行数
     */
    int batchUpdateSortOrder(@Param("fonts") List<Font> fonts);
}
