package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 字体管理实体类
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("font")
public class Font {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字体名称
     */
    @TableField("name")
    private String name;

    /**
     * 字体族名称 (CSS font-family)
     */
    @TableField("family")
    private String family;

    /**
     * 字体类型: system-系统字体, web-Web字体, custom-自定义字体
     */
    @TableField("type")
    private String type;

    /**
     * 字体文件URL (自定义字体)
     */
    @TableField("font_url")
    private String fontUrl;

    /**
     * 字体CSS URL (Web字体)
     */
    @TableField("css_url")
    private String cssUrl;

    /**
     * 字体文件原始名称
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 字体文件存储名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 字体文件大小 (字节)
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 字体文件格式 (ttf, otf, woff, woff2)
     */
    @TableField("file_format")
    private String fileFormat;

    /**
     * 预览文本
     */
    @TableField("preview_text")
    private String previewText;

    /**
     * 字体描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否启用: 0-禁用, 1-启用
     */
    @TableField("enabled")
    private Integer enabled;

    /**
     * 排序权重
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
