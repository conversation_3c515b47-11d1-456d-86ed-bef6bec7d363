<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.web.lowcode.mapper.FontMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.web.lowcode.entity.Font">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="family" property="family" />
        <result column="type" property="type" />
        <result column="font_url" property="fontUrl" />
        <result column="css_url" property="cssUrl" />
        <result column="original_name" property="originalName" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="file_format" property="fileFormat" />
        <result column="preview_text" property="previewText" />
        <result column="description" property="description" />
        <result column="enabled" property="enabled" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, family, type, font_url, css_url, original_name, file_name, 
        file_size, file_format, preview_text, description, enabled, sort_order,
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 分页查询字体列表 -->
    <select id="selectFontPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 查询启用的字体列表 -->
    <select id="selectEnabledFonts" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0 AND enabled = 1
        ORDER BY 
            CASE type 
                WHEN 'system' THEN 1
                WHEN 'web' THEN 2
                WHEN 'custom' THEN 3
                ELSE 4
            END,
            sort_order ASC, 
            create_time DESC
    </select>

    <!-- 根据类型查询字体列表 -->
    <select id="selectFontsByType" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0 AND type = #{type}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 检查字体名称是否存在 -->
    <select id="checkNameExists" resultType="int">
        SELECT COUNT(*)
        FROM font
        WHERE deleted = 0 
        AND name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查字体族是否存在 -->
    <select id="checkFamilyExists" resultType="int">
        SELECT COUNT(*)
        FROM font
        WHERE deleted = 0 
        AND family = #{family}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 更新字体启用状态 -->
    <update id="updateEnabledStatus">
        UPDATE font
        SET enabled = #{enabled},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 批量更新排序 -->
    <update id="batchUpdateSortOrder">
        <foreach collection="fonts" item="font" separator=";">
            UPDATE font
            SET sort_order = #{font.sortOrder},
                update_time = NOW()
            WHERE id = #{font.id} AND deleted = 0
        </foreach>
    </update>

    <!-- 根据文件名查询字体 -->
    <select id="selectByFileName" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0 AND file_name = #{fileName}
        LIMIT 1
    </select>

    <!-- 查询字体统计信息 -->
    <select id="selectFontStatistics" resultType="map">
        SELECT 
            type,
            CASE type 
                WHEN 'system' THEN '系统字体'
                WHEN 'web' THEN 'Web字体'
                WHEN 'custom' THEN '自定义字体'
                ELSE '其他'
            END AS type_name,
            COUNT(*) as total_count,
            COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_count,
            COUNT(CASE WHEN enabled = 0 THEN 1 END) as disabled_count,
            COALESCE(SUM(CASE WHEN file_size IS NOT NULL THEN file_size END), 0) as total_file_size,
            COALESCE(AVG(CASE WHEN file_size IS NOT NULL THEN file_size END), 0) as avg_file_size
        FROM font 
        WHERE deleted = 0
        GROUP BY type
        ORDER BY 
            CASE type 
                WHEN 'system' THEN 1
                WHEN 'web' THEN 2
                WHEN 'custom' THEN 3
                ELSE 4
            END
    </select>

    <!-- 查询最近上传的字体 -->
    <select id="selectRecentFonts" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询大文件字体 -->
    <select id="selectLargeFonts" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM font
        WHERE deleted = 0 
        AND file_size IS NOT NULL 
        AND file_size > #{minSize}
        ORDER BY file_size DESC
    </select>

    <!-- 查询未使用的字体 -->
    <select id="selectUnusedFonts" resultMap="BaseResultMap">
        SELECT 
            f.<include refid="Base_Column_List" />
        FROM font f
        LEFT JOIN font_usage_log ful ON f.id = ful.font_id
        WHERE f.deleted = 0 
        AND ful.font_id IS NULL
        ORDER BY f.create_time DESC
    </select>

    <!-- 软删除字体 -->
    <update id="softDeleteFont">
        UPDATE font
        SET deleted = 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量软删除字体 -->
    <update id="batchSoftDeleteFonts">
        UPDATE font
        SET deleted = 1,
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 恢复已删除的字体 -->
    <update id="restoreFont">
        UPDATE font
        SET deleted = 0,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 永久删除字体 -->
    <delete id="permanentDeleteFont">
        DELETE FROM font WHERE id = #{id}
    </delete>

    <!-- 清理过期的已删除字体 -->
    <delete id="cleanExpiredDeletedFonts">
        DELETE FROM font 
        WHERE deleted = 1 
        AND update_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

</mapper>
