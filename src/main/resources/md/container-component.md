# 容器组件使用文档

## 组件概述

容器组件是布局的基础组件，用于包含和组织其他组件。提供灵活的布局方式和样式控制，是构建复杂页面结构的重要工具。

## 基本信息

- **组件类型**: `container`
- **组件分类**: 布局组件 (layout)
- **图标**: Grid
- **是否系统内置**: 是

## 属性配置

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 | 是否必填 |
|--------|------|--------|------|----------|
| direction | String | "vertical" | 布局方向 | 否 |
| spacing | Number | 0 | 子组件间距 | 否 |
| align | String | "stretch" | 对齐方式 | 否 |
| justify | String | "flex-start" | 主轴对齐 | 否 |
| wrap | Boolean | false | 是否换行 | 否 |

### 属性详细说明

#### direction (布局方向)
- **类型**: String
- **默认值**: "vertical"
- **可选值**:
  - `vertical`: 垂直布局（从上到下）
  - `horizontal`: 水平布局（从左到右）

#### spacing (子组件间距)
- **类型**: Number
- **默认值**: 0
- **单位**: 像素 (px)
- **说明**: 子组件之间的间距

#### align (对齐方式)
- **类型**: String
- **默认值**: "stretch"
- **可选值**:
  - `stretch`: 拉伸填充
  - `flex-start`: 起始对齐
  - `flex-end`: 结束对齐
  - `center`: 居中对齐
  - `baseline`: 基线对齐

#### justify (主轴对齐)
- **类型**: String
- **默认值**: "flex-start"
- **可选值**:
  - `flex-start`: 起始对齐
  - `flex-end`: 结束对齐
  - `center`: 居中对齐
  - `space-between`: 两端对齐
  - `space-around`: 环绕对齐
  - `space-evenly`: 平均分布

#### wrap (是否换行)
- **类型**: Boolean
- **默认值**: false
- **说明**: 当子组件超出容器宽度时是否换行

## 样式配置

### 支持的样式属性

| 样式属性 | 类型 | 默认值 | 说明 |
|----------|------|--------|------|
| width | String | "100%" | 容器宽度 |
| height | String | "auto" | 容器高度 |
| minHeight | String | "auto" | 最小高度 |
| maxHeight | String | "none" | 最大高度 |
| margin | String | "0" | 外边距 |
| padding | String | "0" | 内边距 |
| backgroundColor | String | "transparent" | 背景颜色 |
| border | String | "none" | 边框样式 |
| borderRadius | String | "0" | 圆角半径 |
| boxShadow | String | "none" | 阴影效果 |
| overflow | String | "visible" | 溢出处理 |
| position | String | "relative" | 定位方式 |

### 样式配置示例

```json
{
  "width": "100%",
  "height": "auto",
  "minHeight": "200px",
  "margin": "20px 0",
  "padding": "20px",
  "backgroundColor": "#f5f5f5",
  "border": "1px solid #ddd",
  "borderRadius": "8px",
  "boxShadow": "0 2px 4px rgba(0,0,0,0.1)",
  "overflow": "hidden"
}
```

## 事件配置

### 支持的事件类型

| 事件类型 | 事件名 | 说明 | 触发时机 |
|----------|--------|------|----------|
| 点击 | click | 用户点击容器时触发 | 鼠标单击 |
| 双击 | dblclick | 用户双击容器时触发 | 鼠标双击 |
| 鼠标进入 | mouseenter | 鼠标指针进入容器时触发 | 鼠标悬停 |
| 鼠标离开 | mouseleave | 鼠标指针离开容器时触发 | 鼠标离开 |

### 支持的动作类型

#### 1. 切换组件状态 (toggleComponent)
- **说明**: 控制其他组件的显示/隐藏状态
- **配置参数**:
  - `targetComponentId`: 目标组件ID
  - `operation`: 操作类型 ("show" | "hide" | "toggle")
- **使用场景**: 折叠面板、抽屉效果

#### 2. 调用API (api)
- **说明**: 调用后端API接口
- **配置参数**:
  - `apiUrl`: API接口地址
  - `apiMethod`: 请求方法
  - `apiParams`: 请求参数
- **使用场景**: 数据加载、状态更新

#### 3. 执行JavaScript (javascript)
- **说明**: 执行自定义JavaScript代码
- **配置参数**:
  - `jsCode`: JavaScript代码
- **使用场景**: 复杂交互逻辑

## 布局模式

### 1. 垂直布局 (Vertical)
- 子组件从上到下排列
- 适用于表单、列表等场景
- 宽度自动填充容器

### 2. 水平布局 (Horizontal)
- 子组件从左到右排列
- 适用于工具栏、按钮组等场景
- 高度自动适应内容

### 3. 网格布局 (Grid)
- 结合CSS Grid实现复杂布局
- 支持响应式设计
- 精确控制组件位置

### 4. 弹性布局 (Flexbox)
- 基于Flexbox的灵活布局
- 自动分配空间
- 支持对齐和分布控制

## 使用步骤

### 1. 添加组件
1. 从组件面板的"布局组件"分类中拖拽"容器"组件到画布
2. 容器会以默认配置添加到页面中

### 2. 配置布局
1. 选中容器组件
2. 在右侧属性面板中配置：
   - **布局方向**: 选择垂直或水平布局
   - **子组件间距**: 设置组件之间的间距
   - **对齐方式**: 设置子组件的对齐方式
   - **主轴对齐**: 设置主轴方向的对齐方式
   - **是否换行**: 控制子组件是否换行

### 3. 设置样式
1. 在样式面板中配置：
   - **尺寸**: 设置容器宽度和高度
   - **边距**: 设置外边距和内边距
   - **背景**: 设置背景颜色
   - **边框**: 设置边框样式和圆角
   - **阴影**: 添加阴影效果
   - **溢出**: 控制内容溢出处理

### 4. 添加子组件
1. 将其他组件拖拽到容器内部
2. 子组件会根据容器的布局配置自动排列
3. 可以调整子组件的顺序和属性

### 5. 配置事件（可选）
1. 切换到"事件"选项卡
2. 根据需要添加交互事件
3. 配置相应的动作

## 常用布局示例

### 1. 卡片容器
```json
{
  "props": {
    "direction": "vertical",
    "spacing": 16,
    "align": "stretch"
  },
  "styles": {
    "padding": "20px",
    "backgroundColor": "#ffffff",
    "border": "1px solid #e8e8e8",
    "borderRadius": "8px",
    "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"
  }
}
```

### 2. 工具栏容器
```json
{
  "props": {
    "direction": "horizontal",
    "spacing": 12,
    "justify": "space-between",
    "align": "center"
  },
  "styles": {
    "padding": "12px 16px",
    "backgroundColor": "#f5f5f5",
    "borderBottom": "1px solid #ddd"
  }
}
```

### 3. 表单容器
```json
{
  "props": {
    "direction": "vertical",
    "spacing": 20,
    "align": "stretch"
  },
  "styles": {
    "maxWidth": "600px",
    "margin": "0 auto",
    "padding": "30px",
    "backgroundColor": "#ffffff",
    "borderRadius": "12px"
  }
}
```

## 高级功能

### 响应式布局
- 使用媒体查询实现不同屏幕尺寸的布局适配
- 动态调整布局方向和间距

### 嵌套容器
- 容器内可以嵌套其他容器
- 实现复杂的多层级布局结构

### 条件显示
- 根据数据状态动态显示/隐藏容器
- 实现动态布局效果

## 常见问题

### Q: 如何实现响应式布局？
A: 使用百分比宽度、媒体查询和弹性布局属性，结合JavaScript动态调整布局参数。

### Q: 容器内的组件如何居中？
A: 设置align为"center"和justify为"center"实现完全居中。

### Q: 如何实现固定高度的滚动容器？
A: 设置固定高度和overflow为"auto"或"scroll"。

### Q: 容器背景色不显示怎么办？
A: 确保容器有内容或设置了最小高度，空容器可能不显示背景。

## 最佳实践

1. **语义化布局**: 根据内容特点选择合适的布局方式
2. **间距统一**: 保持页面中容器间距的一致性
3. **响应式设计**: 考虑不同屏幕尺寸下的布局效果
4. **性能优化**: 避免过度嵌套容器
5. **无障碍访问**: 确保布局结构清晰，便于屏幕阅读器理解
6. **视觉层次**: 使用背景色、边框、阴影等建立视觉层次
7. **内容优先**: 布局应该服务于内容，而不是限制内容
