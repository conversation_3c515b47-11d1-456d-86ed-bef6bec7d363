# 文本域组件使用文档

## 组件概述

文本域组件用于多行文本输入，适合输入较长的文本内容如评论、描述、备注等。支持自动调整高度、字符计数和丰富的验证功能。

## 基本信息

- **组件类型**: `textarea`
- **组件分类**: 表单组件 (form)
- **图标**: Document
- **是否系统内置**: 是

## 属性配置

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 | 是否必填 |
|--------|------|--------|------|----------|
| placeholder | String | "请输入内容" | 占位文本 | 否 |
| defaultValue | String | "" | 默认值 | 否 |
| rows | Number | 3 | 显示行数 | 否 |
| maxLength | Number | null | 最大字符长度 | 否 |
| disabled | Boolean | false | 是否禁用 | 否 |
| readonly | Boolean | false | 是否只读 | 否 |
| autosize | Boolean | false | 是否自动调整高度 | 否 |
| showWordLimit | Boolean | false | 是否显示字符计数 | 否 |
| resize | String | "vertical" | 调整尺寸方式 | 否 |

### 属性详细说明

#### placeholder (占位文本)
- **类型**: String
- **默认值**: "请输入内容"
- **说明**: 文本域为空时显示的提示文本

#### defaultValue (默认值)
- **类型**: String
- **默认值**: ""
- **说明**: 文本域的初始值
- **支持功能**: 支持数据绑定表达式

#### rows (显示行数)
- **类型**: Number
- **默认值**: 3
- **说明**: 文本域显示的行数，影响初始高度

#### maxLength (最大字符长度)
- **类型**: Number
- **默认值**: null
- **说明**: 限制输入的最大字符数，null表示不限制

#### disabled (禁用状态)
- **类型**: Boolean
- **默认值**: false
- **说明**: 控制文本域是否可编辑

#### readonly (只读状态)
- **类型**: Boolean
- **默认值**: false
- **说明**: 设置为只读，可选择但不可编辑

#### autosize (自动调整高度)
- **类型**: Boolean
- **默认值**: false
- **说明**: 根据内容自动调整文本域高度

#### showWordLimit (显示字符计数)
- **类型**: Boolean
- **默认值**: false
- **说明**: 是否显示当前字符数/最大字符数

#### resize (调整尺寸方式)
- **类型**: String
- **默认值**: "vertical"
- **可选值**:
  - `none`: 不允许调整
  - `vertical`: 只允许垂直调整
  - `horizontal`: 只允许水平调整
  - `both`: 允许双向调整

## 样式配置

### 支持的样式属性

| 样式属性 | 类型 | 默认值 | 说明 |
|----------|------|--------|------|
| width | String | "100%" | 文本域宽度 |
| height | String | "auto" | 文本域高度 |
| minHeight | String | "auto" | 最小高度 |
| maxHeight | String | "auto" | 最大高度 |
| margin | String | "0" | 外边距 |
| padding | String | "8px 12px" | 内边距 |
| fontSize | String | "14px" | 字体大小 |
| lineHeight | String | "1.5" | 行高 |
| color | String | "#333" | 文字颜色 |
| backgroundColor | String | "#fff" | 背景颜色 |
| border | String | "1px solid #ddd" | 边框样式 |
| borderRadius | String | "4px" | 圆角半径 |
| boxShadow | String | "none" | 阴影效果 |

### 样式配置示例

```json
{
  "width": "100%",
  "minHeight": "120px",
  "maxHeight": "300px",
  "margin": "10px 0",
  "padding": "12px 16px",
  "fontSize": "16px",
  "lineHeight": "1.6",
  "color": "#333333",
  "backgroundColor": "#ffffff",
  "border": "2px solid #409eff",
  "borderRadius": "8px",
  "boxShadow": "0 2px 4px rgba(0,0,0,0.1)"
}
```

## 事件配置

### 支持的事件类型

| 事件类型 | 事件名 | 说明 | 触发时机 |
|----------|--------|------|----------|
| 输入变化 | input | 输入内容变化时触发 | 实时输入 |
| 值变化 | change | 输入完成后触发 | 失去焦点 |
| 获得焦点 | focus | 文本域获得焦点时触发 | 点击或Tab键 |
| 失去焦点 | blur | 文本域失去焦点时触发 | 点击其他区域 |
| 点击 | click | 点击文本域时触发 | 鼠标单击 |
| 按键按下 | keydown | 按键按下时触发 | 键盘按下 |
| 按键释放 | keyup | 按键释放时触发 | 键盘释放 |

### 支持的动作类型

#### 1. 数据验证 (validate)
- **说明**: 验证输入内容的有效性
- **配置参数**:
  - `rules`: 验证规则数组
  - `errorMessage`: 错误提示信息
- **使用场景**: 表单验证、内容检查

#### 2. 调用API (api)
- **说明**: 调用后端API接口
- **配置参数**:
  - `apiUrl`: API接口地址
  - `apiMethod`: 请求方法
  - `apiParams`: 请求参数
- **使用场景**: 自动保存、内容分析

#### 3. 设置变量 (setVariable)
- **说明**: 将输入值设置到页面变量
- **配置参数**:
  - `variableName`: 变量名称
  - `variableValue`: 变量值
- **使用场景**: 数据传递、状态管理

#### 4. 字符计数 (countCharacters)
- **说明**: 实时统计字符数量
- **配置参数**:
  - `showCount`: 是否显示计数
  - `maxCount`: 最大字符数
- **使用场景**: 内容长度控制

#### 5. 执行JavaScript (javascript)
- **说明**: 执行自定义JavaScript代码
- **配置参数**:
  - `jsCode`: JavaScript代码
- **使用场景**: 内容格式化、自动补全

## 数据验证

### 内置验证规则

| 规则类型 | 说明 | 配置示例 |
|----------|------|----------|
| required | 必填验证 | `{"required": true, "message": "此字段为必填项"}` |
| minLength | 最小长度 | `{"min": 10, "message": "至少输入10个字符"}` |
| maxLength | 最大长度 | `{"max": 500, "message": "最多输入500个字符"}` |
| pattern | 正则表达式 | `{"pattern": "/^[\\s\\S]*$/", "message": "格式不正确"}` |
| wordCount | 单词数量 | `{"wordCount": {"min": 5, "max": 100}, "message": "单词数量应在5-100之间"}` |

### 验证配置示例

```json
{
  "rules": [
    {"required": true, "message": "内容不能为空"},
    {"min": 20, "message": "内容至少20个字符"},
    {"max": 1000, "message": "内容最多1000个字符"}
  ]
}
```

## 使用步骤

### 1. 添加组件
1. 从组件面板的"表单组件"分类中拖拽"文本域"组件到画布
2. 组件会以默认配置添加到页面中

### 2. 配置属性
1. 选中文本域组件
2. 在右侧属性面板中配置：
   - **占位文本**: 设置提示信息
   - **默认值**: 设置初始内容
   - **显示行数**: 设置初始高度
   - **最大长度**: 限制输入字符数
   - **自动调整**: 启用高度自动调整
   - **字符计数**: 显示字符统计
   - **调整方式**: 设置用户调整尺寸的方式

### 3. 设置样式
1. 在样式面板中配置：
   - **尺寸**: 设置宽度和高度范围
   - **边距**: 设置外边距和内边距
   - **字体**: 设置字体大小和行高
   - **颜色**: 设置文字和背景颜色
   - **边框**: 设置边框样式和圆角
   - **阴影**: 添加阴影效果

### 4. 配置验证
1. 在验证面板中设置：
   - **验证规则**: 添加必填、长度等验证
   - **错误提示**: 设置验证失败的提示信息
   - **验证时机**: 选择验证触发时机

### 5. 配置事件
1. 切换到"事件"选项卡
2. 添加相应的事件处理：
   - **输入变化**: 实时处理输入内容
   - **失去焦点**: 完成输入后的处理
   - **字符计数**: 监控内容长度

## 常用配置示例

### 1. 评论输入框
```json
{
  "props": {
    "placeholder": "请输入您的评论...",
    "rows": 4,
    "maxLength": 500,
    "autosize": true,
    "showWordLimit": true
  },
  "validation": {
    "rules": [
      {"required": true, "message": "评论不能为空"},
      {"min": 5, "message": "评论至少5个字符"}
    ]
  }
}
```

### 2. 文章内容编辑
```json
{
  "props": {
    "placeholder": "请输入文章内容...",
    "rows": 10,
    "maxLength": 5000,
    "autosize": false,
    "showWordLimit": true,
    "resize": "vertical"
  },
  "styles": {
    "minHeight": "200px",
    "maxHeight": "600px"
  }
}
```

### 3. 反馈意见框
```json
{
  "props": {
    "placeholder": "请详细描述您的问题或建议...",
    "rows": 6,
    "maxLength": 1000,
    "autosize": true,
    "showWordLimit": true
  },
  "events": [
    {
      "type": "blur",
      "action": {
        "type": "api",
        "apiUrl": "/api/save-draft",
        "apiMethod": "post"
      }
    }
  ]
}
```

## 高级功能

### 自动保存
- 配置定时器或失去焦点事件自动保存内容
- 防止用户意外丢失输入内容

### 内容格式化
- 使用JavaScript事件自动格式化输入内容
- 如自动添加段落、转换格式等

### 智能提示
- 结合API实现内容智能提示
- 提供写作建议或自动补全

### 富文本预览
- 支持Markdown或其他标记语言的实时预览
- 提供所见即所得的编辑体验

## 常见问题

### Q: 如何实现文本域的自动保存功能？
A: 配置input或blur事件调用保存API，建议使用防抖技术避免频繁请求。

### Q: 文本域高度不能自动调整怎么办？
A: 确保autosize属性设置为true，检查CSS样式是否有冲突。

### Q: 如何限制输入特定格式的内容？
A: 使用正则表达式验证规则或JavaScript事件过滤不符合要求的字符。

### Q: 字符计数不准确怎么办？
A: 检查maxLength设置和showWordLimit配置，确保计数逻辑正确。

## 最佳实践

1. **用户体验**: 提供清晰的占位文本和字符计数
2. **内容保护**: 实现自动保存功能防止内容丢失
3. **性能优化**: 对于长文本使用防抖技术优化性能
4. **无障碍访问**: 确保文本域有适当的标签和描述
5. **移动适配**: 考虑移动设备上的输入体验
6. **内容验证**: 前后端结合验证确保内容质量
7. **视觉设计**: 合理设置高度和边距提升视觉效果
8. **功能增强**: 根据使用场景添加格式化、预览等功能
