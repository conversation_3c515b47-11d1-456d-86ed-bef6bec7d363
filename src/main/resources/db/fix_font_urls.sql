-- 修复字体URL路径
-- 将错误的 /fonts/ 路径更新为正确的 /api/fonts/file/ 路径

-- 查看当前的字体URL格式
SELECT id, name, font_url, type 
FROM font 
WHERE type = 'custom' AND font_url IS NOT NULL;

-- 更新字体URL路径
UPDATE font 
SET font_url = REPLACE(font_url, '/fonts/', '/api/fonts/file/')
WHERE type = 'custom' 
  AND font_url IS NOT NULL 
  AND font_url LIKE '%/fonts/%';

-- 验证更新结果
SELECT id, name, font_url, type 
FROM font 
WHERE type = 'custom' AND font_url IS NOT NULL;

-- 如果需要完全重新生成URL（假设端口是8080，context path为空）
-- UPDATE font 
-- SET font_url = CONCAT('http://localhost:8080/api/fonts/file/', file_name)
-- WHERE type = 'custom' 
--   AND file_name IS NOT NULL;
