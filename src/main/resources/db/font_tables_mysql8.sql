-- 字体管理相关表结构 (MySQL 8.0)
-- 创建时间: 2024-01-20

-- 删除已存在的表
DROP TABLE IF EXISTS `font`;

-- 创建字体表
CREATE TABLE `font` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '字体名称',
    `family` VARCHAR(200) NOT NULL COMMENT '字体族名称 (CSS font-family)',
    `type` VARCHAR(20) NOT NULL DEFAULT 'custom' COMMENT '字体类型: system-系统字体, web-Web字体, custom-自定义字体',
    `font_url` VARCHAR(500) NULL COMMENT '字体文件URL (自定义字体)',
    `css_url` VARCHAR(500) NULL COMMENT '字体CSS URL (Web字体)',
    `original_name` VARCHAR(255) NULL COMMENT '字体文件原始名称',
    `file_name` VARCHAR(255) NULL COMMENT '字体文件存储名称',
    `file_size` BIGINT NULL COMMENT '字体文件大小 (字节)',
    `file_format` VARCHAR(10) NULL COMMENT '字体文件格式 (ttf, otf, woff, woff2)',
    `preview_text` VARCHAR(100) NOT NULL DEFAULT 'Aa' COMMENT '预览文本',
    `description` TEXT NULL COMMENT '字体描述',
    `enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用: 0-禁用, 1-启用',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序权重',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) NULL COMMENT '创建人',
    `update_by` VARCHAR(50) NULL COMMENT '更新人',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记: 0-正常, 1-删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_font_name` (`name`, `deleted`),
    UNIQUE KEY `uk_font_family` (`family`, `deleted`),
    KEY `idx_font_type` (`type`),
    KEY `idx_font_enabled` (`enabled`),
    KEY `idx_font_sort` (`sort_order`),
    KEY `idx_font_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字体管理表';

-- 插入默认系统字体
INSERT INTO `font` (`name`, `family`, `type`, `description`, `preview_text`, `enabled`, `sort_order`) VALUES
('Arial', 'Arial, sans-serif', 'system', 'Arial字体，经典的无衬线字体', 'Arial Font', 1, 1),
('Times New Roman', '"Times New Roman", serif', 'system', 'Times New Roman字体，经典的衬线字体', 'Times Font', 1, 2),
('Helvetica', 'Helvetica, Arial, sans-serif', 'system', 'Helvetica字体，现代无衬线字体', 'Helvetica', 1, 3),
('Georgia', 'Georgia, serif', 'system', 'Georgia字体，适合阅读的衬线字体', 'Georgia', 1, 4),
('Verdana', 'Verdana, sans-serif', 'system', 'Verdana字体，清晰的无衬线字体', 'Verdana', 1, 5),
('宋体', '"SimSun", "宋体", serif', 'system', '宋体，中文经典衬线字体', '宋体字体', 1, 10),
('黑体', '"SimHei", "黑体", sans-serif', 'system', '黑体，中文经典无衬线字体', '黑体字体', 1, 11),
('微软雅黑', '"Microsoft YaHei", "微软雅黑", sans-serif', 'system', '微软雅黑，现代中文字体', '微软雅黑', 1, 12),
('楷体', '"KaiTi", "楷体", serif', 'system', '楷体，中文手写风格字体', '楷体字体', 1, 13),
('仿宋', '"FangSong", "仿宋", serif', 'system', '仿宋，中文传统字体', '仿宋字体', 1, 14);

-- 插入一些常用的Web字体示例
INSERT INTO `font` (`name`, `family`, `type`, `css_url`, `description`, `preview_text`, `enabled`, `sort_order`) VALUES
('Google Fonts - Roboto', 'Roboto, sans-serif', 'web', 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap', 'Google Fonts的Roboto字体', 'Roboto Font', 1, 20),
('Google Fonts - Open Sans', '"Open Sans", sans-serif', 'web', 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap', 'Google Fonts的Open Sans字体', 'Open Sans', 1, 21),
('Google Fonts - Lato', 'Lato, sans-serif', 'web', 'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap', 'Google Fonts的Lato字体', 'Lato Font', 1, 22),
('Google Fonts - Montserrat', 'Montserrat, sans-serif', 'web', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap', 'Google Fonts的Montserrat字体', 'Montserrat', 1, 23);

-- 创建字体使用统计表 (可选)
CREATE TABLE `font_usage_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `font_id` BIGINT NOT NULL COMMENT '字体ID',
    `project_id` BIGINT NULL COMMENT '项目ID',
    `page_id` BIGINT NULL COMMENT '页面ID',
    `component_id` VARCHAR(100) NULL COMMENT '组件ID',
    `usage_type` VARCHAR(20) NOT NULL DEFAULT 'apply' COMMENT '使用类型: apply-应用, preview-预览, download-下载',
    `user_agent` TEXT NULL COMMENT '用户代理',
    `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_font_usage_font_id` (`font_id`),
    KEY `idx_font_usage_project_id` (`project_id`),
    KEY `idx_font_usage_page_id` (`page_id`),
    KEY `idx_font_usage_type` (`usage_type`),
    KEY `idx_font_usage_create_time` (`create_time`),
    CONSTRAINT `fk_font_usage_font_id` FOREIGN KEY (`font_id`) REFERENCES `font` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字体使用统计表';

-- 创建字体统计视图
CREATE OR REPLACE VIEW `v_font_statistics` AS
SELECT 
    f.id,
    f.name,
    f.family,
    f.type,
    f.enabled,
    f.file_size,
    f.create_time,
    COALESCE(usage_stats.total_usage, 0) as total_usage,
    COALESCE(usage_stats.apply_count, 0) as apply_count,
    COALESCE(usage_stats.preview_count, 0) as preview_count,
    COALESCE(usage_stats.download_count, 0) as download_count,
    COALESCE(usage_stats.last_used_time, NULL) as last_used_time
FROM `font` f
LEFT JOIN (
    SELECT 
        font_id,
        COUNT(*) as total_usage,
        COUNT(CASE WHEN usage_type = 'apply' THEN 1 END) as apply_count,
        COUNT(CASE WHEN usage_type = 'preview' THEN 1 END) as preview_count,
        COUNT(CASE WHEN usage_type = 'download' THEN 1 END) as download_count,
        MAX(create_time) as last_used_time
    FROM `font_usage_log`
    GROUP BY font_id
) usage_stats ON f.id = usage_stats.font_id
WHERE f.deleted = 0;

-- 创建字体类型统计视图
CREATE OR REPLACE VIEW `v_font_type_statistics` AS
SELECT 
    type,
    CASE type 
        WHEN 'system' THEN '系统字体'
        WHEN 'web' THEN 'Web字体'
        WHEN 'custom' THEN '自定义字体'
        ELSE '其他'
    END AS type_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_count,
    COUNT(CASE WHEN enabled = 0 THEN 1 END) as disabled_count,
    COALESCE(SUM(CASE WHEN file_size IS NOT NULL THEN file_size END), 0) as total_file_size,
    COALESCE(AVG(CASE WHEN file_size IS NOT NULL THEN file_size END), 0) as avg_file_size
FROM `font` 
WHERE deleted = 0
GROUP BY type
ORDER BY 
    CASE type 
        WHEN 'system' THEN 1
        WHEN 'web' THEN 2
        WHEN 'custom' THEN 3
        ELSE 4
    END;

-- 添加索引优化
ALTER TABLE `font` ADD INDEX `idx_font_type_enabled` (`type`, `enabled`);
ALTER TABLE `font` ADD INDEX `idx_font_name_type` (`name`, `type`);

-- 添加注释
ALTER TABLE `font` COMMENT = '字体管理表 - 存储系统字体、Web字体和自定义字体信息';
ALTER TABLE `font_usage_log` COMMENT = '字体使用统计表 - 记录字体的使用情况';

-- 显示表结构
SHOW CREATE TABLE `font`;
SHOW CREATE TABLE `font_usage_log`;

-- 查询初始数据
SELECT * FROM `font` ORDER BY `type`, `sort_order`;
SELECT * FROM `v_font_type_statistics`;
